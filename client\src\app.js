/**
 * Main Application Controller for Audio Agent Client
 */

class AudioAgentApp {
  constructor() {
    this.wsManager = new WebSocketManager();
    this.audioManager = new AudioManager();

    // Application state
    this.isConnected = false;
    this.isRecording = false;
    this.isPlaying = false;
    this.sessionId = null;
    this.serverUrl = "ws://localhost:8000/ws/conversation";
    this.selectedMode = "standard"; // 默认模式
    this.isInterruptedByAI = false; // 🎯 新增状态，标记是否被AI打断

    // UI elements
    this.elements = {};
    this.conversation = [];

    // Chat management
    this.chatManager = null;
    this.currentTypingMessageId = null;
    this.currentStreamingMessageId = null;

    // 添加流式响应缓存
    this.streamingResponse = "";
    this.isStreamingResponse = false;

    // 🎯 改进：转录源优先级管理
    this.currentTranscriptSource = null; // 当前转录源（paraformer/qwen）
    this.lastParaformerTranscript = null; // 最后一次paraformer转录结果，用于去重
    this.currentTranscriptText = ""; // 当前显示的转录文本，用于增量更新

    // 🎯 改进：智能转录缓冲和时序协调机制
    this.transcriptBuffer = {
      qwen: null,           // qwen转录结果缓存
      paraformer: null,     // paraformer转录结果缓存
      qwenTimestamp: null,  // qwen转录时间戳
      paraformerTimestamp: null, // paraformer转录时间戳
      waitingForParaformer: false, // 是否正在等待paraformer结果
      maxWaitTime: 1500,    // 🎯 优化：减少等待时间到1.5秒，提高响应性
      pendingAIResponses: [], // 待显示的AI响应队列
      timeoutId: null,      // 等待超时定时器ID
      lastProcessedText: "", // 最后处理的文本，用于去重
      qualityScore: {       // 转录质量评分
        qwen: 0,
        paraformer: 0
      }
    };

    // Settings
    this.settings = {
      autoRecord: false,
      showDebugLog: false,
      audioVisualization: true,
      volume: 80,
    };

    // Initialize the application
    this.initialize();
  }

  async initialize() {
    console.log("🚀 Initializing Audio Agent Client...");

    try {
      this.initializeElements();
      this.setupEventHandlers();
      this.setupWebSocketHandlers();
      this.setupAudioHandlers();

      await this.audioManager.initialize();
      // 添加音频设备检测
      await this.initializeAudioDevices();

      this.updateConnectionStatus(false);
      this.updateRecordingStatus(false);

      console.log("✅ Audio Agent Client initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize application:", error);
      this.showError("Failed to initialize application: " + error.message);
    }
  }

  initializeElements() {
    this.elements = {
      // 新的对话按钮
      conversationBtn: document.getElementById("conversationBtn"),
      modeSelection: document.getElementById("modeSelection"),
      // 模式选择按钮
      modeStandard: document.getElementById("mode-standard"),
      modeInterviewer: document.getElementById("mode-interviewer"),
      // 聊天界面元素
      chatMessages: document.getElementById("chatMessages"),
      transcriptStatusContainer: document.getElementById(
        "transcriptStatusContainer"
      ),
      transcriptStatus: document.getElementById("transcriptStatus"),
      aiStatus: document.getElementById("aiStatus"),
      // 音频可视化
      visualizer: document.getElementById("visualizer"),
      volumeMeter: document.getElementById("volumeMeter"),
      inputVolume: document.getElementById("input-volume"),
      // 其他控制元素
      clearBtn: document.getElementById("clearBtn"),
      errorContainer: document.getElementById("errorContainer"),
      // Header状态指示器
      statusIndicator: document.getElementById("status-indicator"),
      statusText: document.getElementById("status-text"),
    };

    // 初始化聊天管理器
    if (this.elements.chatMessages) {
      this.chatManager = new ChatMessageManager(this.elements.chatMessages);
      console.log("🎯 Chat manager initialized");

      // 🎯 调试：检查聊天容器状态
      console.log(`🎯 DEBUG: Chat container status - exists: ${!!this.elements.chatMessages}, visible: ${this.elements.chatMessages.offsetHeight > 0}, children: ${this.elements.chatMessages.children.length}`);
    } else {
      console.error("❌ Chat messages container not found!");
    }

    // 初始化对话按钮状态
    this.updateConversationButton("disconnected");
  }

  setupEventHandlers() {
    // 主对话按钮事件处理
    this.elements.conversationBtn?.addEventListener("click", () => {
      this.handleConversationButtonClick();
    });

    // 清空对话按钮
    this.elements.clearBtn?.addEventListener("click", () =>
      this.clearConversation()
    );

    this.elements.debugToggle?.addEventListener("change", (e) => {
      this.settings.showDebugLog = e.target.checked;
      this.toggleDebugLog();
    });

    this.elements.autoRecordToggle?.addEventListener("change", (e) => {
      this.settings.autoRecord = e.target.checked;
    });

    this.elements.serverUrlInput?.addEventListener("change", (e) => {
      this.serverUrl = e.target.value;
    });

    // 统一的模式选择按钮事件监听器
    document.querySelectorAll(".mode-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        this.handleModeSelection(e);
      });
    });

    document.addEventListener("keydown", (e) => {
      if (e.code === "Space" && e.ctrlKey) {
        e.preventDefault();
        if (this.isRecording) {
          this.stopRecording();
        } else {
          this.startRecording();
        }
      }
    });

    window.addEventListener("beforeunload", () => {
      this.cleanup();
    });
  }

  setupWebSocketHandlers() {
    this.wsManager.onConnectionChange = (connected) => {
      this.isConnected = connected;
      this.updateConnectionStatus(connected);

      if (connected) {
        this.sessionId = this.wsManager.sessionId;
        this.updateSessionInfo();
        this.addDebugMessage("WebSocket connected");

        // 处理待发送的模式选择
        if (this.pendingMode) {
          this.addDebugMessage(`发送待处理的模式选择: ${this.pendingMode}`);
          this.sendModeSelection(this.pendingMode);
          this.pendingMode = null;
        }
      } else {
        this.addDebugMessage("WebSocket disconnected");
      }
    };

    this.wsManager.onTranscript = (message) => {
      this.handleTranscriptMessage(message);
    };

    this.wsManager.onAIResponse = (message) => {
      this.handleAIResponseMessage(message);
    };

    this.wsManager.onAudioOutput = (message) => {
      this.handleAudioOutputMessage(message);
    };

    // 🎯 新增：音频播放停止处理
    this.wsManager.onAudioPlaybackStop = (message) => {
      console.log("🛑 FRONTEND: Received audio playback stop command", message);
      this.audioManager.stopAudio();
      this.addDebugMessage("Audio playback stopped by server command");
    };

    this.wsManager.onError = (error) => {
      this.showError(error);
    };

    this.wsManager.onDebug = (message) => {
      this.addDebugMessage(message);
    };

    // Enhanced event handlers
    this.wsManager.onSpeechStarted = (message) => {
      this.handleSpeechStarted(message);
    };

    this.wsManager.onSpeechStopped = (message) => {
      this.handleSpeechStopped(message);
    };

    this.wsManager.onConnectionQualityChange = (newQuality, oldQuality) => {
      this.handleConnectionQualityChange(newQuality, oldQuality);
    };

    // 🎯 新增：打断消息处理器
    // 🎯 修复: 创建一个全新的处理器，专门处理打断控制消息
    this.wsManager.onControlInterruption = (message) => {
      console.log(
        "✅ FRONTEND: onControlInterruption handler triggered!",
        message
      );
      this.handleControlInterruption(message);
    };

    // 保留旧的 onInterruption 处理器，用于处理非控制类的UI更新
    this.wsManager.onInterruption = (message) => {
      console.log(
        "✅ FRONTEND: onInterruption (UI) handler triggered!",
        message
      );
      // 这里可以处理一些UI提示，但不应控制录音
    };

    // 🎯 新增：合作型附和消息处理器
    this.wsManager.onCooperativeInterjection = (message) => {
      this.handleCooperativeInterjection(message);
    };

    // 🎯 第三种方案：音频控制消息处理器
    this.wsManager.onAudioControl = (message) => {
      console.log("🎤 FRONTEND: Audio control message received:", message);
      this.handleAudioControl(message);
    };
  }

  setupAudioHandlers() {
    this.audioManager.onRecordingStart = () => {
      this.isRecording = true;
      this.updateRecordingStatus(true);
      this.updateAudioStatus(); // 添加状态更新
      this.addDebugMessage("Recording started");

      if (this.settings.audioVisualization) {
        this.startAudioVisualization();
      }
    };

    this.audioManager.onRecordingStop = () => {
      this.isRecording = false;
      this.updateRecordingStatus(false);
      this.updateAudioStatus(); // 添加状态更新
      this.addDebugMessage("Recording stopped");
      this.stopAudioVisualization();
    };

    this.audioManager.onAudioData = (audioData) => {
      if (this.isConnected) {
        this.wsManager.sendAudio(audioData);
      }
    };

    this.audioManager.onPlaybackStart = () => {
      this.isPlaying = true;
      this.updateAudioStatus(); // 添加状态更新
      this.addDebugMessage("Audio playback started");
    };

    this.audioManager.onPlaybackEnd = () => {
      this.isPlaying = false;
      this.updateAudioStatus(); // 添加状态更新
      this.addDebugMessage("Audio playback finished");
    };

    this.audioManager.onError = (error) => {
      this.showError("Audio error: " + error);
    };
  }

  handleAudioOutputMessage(message) {
    try {
      // 🎯 统一音频数据提取逻辑
      let audioData, format, sampleRate, source, responseId, chunkId;

      if (message.data) {
        // 新格式：{type: "audio_output", data: "base64_data", format: "pcm16", ...}
        audioData = message.data;
        format = message.format || "pcm16";
        sampleRate = message.sample_rate || 24000;
        source = message.source || "unknown";
        responseId = message.response_id;
        chunkId = message.chunk_id;
      } else {
        // 兼容旧格式：{type: "audio_output", data: {audio_data: "base64", ...}}
        const messageData = message.data || message;
        audioData = messageData.audio_data || messageData.data;
        format = messageData.format || "pcm16";
        sampleRate = messageData.sample_rate || 24000;
        source = messageData.source || "legacy";
        responseId = messageData.response_id;
        chunkId = messageData.chunk_id;
      }

      // 🎯 生成chunkId用于去重（如果没有提供）
      if (!chunkId && responseId && audioData) {
        chunkId = `${responseId}_${Date.now()}_${audioData.substring(0, 16)}`;
      }

      console.log("🔊 Received audio output message:", {
        format: format,
        dataLength: audioData ? audioData.length : 0,
        hasData: !!audioData,
        source: source,
        responseId: responseId,
        chunkId: chunkId,
        sampleRate: sampleRate,
        messageType: message.type,
        reassembled: message.reassembled || false,
      });

      if (audioData) {
        // 确保音频上下文已经恢复
        this.ensureAudioContextResumed()
          .then(() => {
            // 🎯 使用统一的音频播放接口，传递chunkId用于去重
            if (format === "pcm16") {
              this.audioManager.directPlayer.playChunk(audioData, chunkId);
            } else {
              this.audioManager.playAudio(audioData, format);
            }

            // 添加详细的调试信息
            const debugInfo = {
              source: source,
              format: format,
              dataLength: audioData.length,
              chunkId: chunkId,
              timestamp: new Date().toISOString(),
            };

            this.addDebugMessage(
              `🎵 播放${source}音频: ${format}, ${
                audioData.length
              }字符, 块ID: ${chunkId || "N/A"}`
            );

            // 如果是直接通道，记录特殊日志
            if (source === "direct_channel") {
              console.log("✅ Direct audio channel working correctly");
              this.addDebugMessage("✅ 直接音频通道工作正常");
            } else if (source && source.includes("fallback")) {
              console.log("⚠️ Using fallback audio channel");
              this.addDebugMessage("⚠️ 使用降级音频通道");
            } else if (source === "message_bus_primary") {
              console.log("📡 Using message bus primary channel");
              this.addDebugMessage("📡 使用消息总线主通道");
            }
          })
          .catch((error) => {
            console.error("Failed to resume audio context:", error);
            this.addDebugMessage("❌ 音频上下文恢复失败: " + error.message);
            this.showError("音频播放失败: " + error.message);
          });
      } else {
        console.warn("⚠️ Audio output message missing audio data:", message);
        this.addDebugMessage("⚠️ 音频消息缺少音频数据");
      }
    } catch (error) {
      console.error("❌ Error handling audio output message:", error);
      this.addDebugMessage("❌ 音频输出处理错误: " + error.message);
      this.showError("音频处理错误: " + error.message);
    }
  }

  /**
   * Handle speech started event
   * @param {Object} message Speech started message
   */
  handleSpeechStarted(message) {
    console.log("🎤 Speech started detected");

    // 添加详细的调试信息
    const audioStatus = this.audioManager?.directPlayer?.getStatus();
    console.log("🎤 AudioManager state:", {
      audioManagerExists: !!this.audioManager,
      isPlaying: this.audioManager?.isPlaying,
      directPlayerIsPlaying: this.audioManager?.directPlayer?.isPlaying,
      appIsPlaying: this.isPlaying,
      activeSourcesCount: audioStatus?.activeSourcesCount || 0,
      queueLength: audioStatus?.queueLength || 0,
      isInterrupted: audioStatus?.isInterrupted || false,
    });

    // Interrupt current audio playback
    if (this.audioManager && this.audioManager.isPlaying) {
      console.log(
        "🎤 handleSpeechInterruption - AudioManager.isPlaying is true"
      );
      this.audioManager.handleSpeechInterruption();
    } else if (
      this.audioManager &&
      this.audioManager.directPlayer &&
      this.audioManager.directPlayer.isPlaying
    ) {
      console.log(
        "🎤 handleSpeechInterruption - DirectPlayer.isPlaying is true"
      );
      this.audioManager.handleSpeechInterruption();
    } else if (this.isPlaying) {
      console.log("🎤 handleSpeechInterruption - App.isPlaying is true");
      this.audioManager.handleSpeechInterruption();
    } else {
      console.log("🎤 No audio playing to interrupt");
    }

    // Update status
    this.updateTranscriptStatus("正在检测语音...");

    // 🎯 修复：不删除AI消息，只是完成当前的流式响应
    if (this.currentStreamingMessageId && this.chatManager) {
      // 将当前流式消息标记为完成，而不是删除
      const currentMessage = this.chatManager.getMessage(
        this.currentStreamingMessageId
      );
      if (currentMessage && currentMessage.status === MessageStatus.STREAMING) {
        this.chatManager.updateMessage(
          this.currentStreamingMessageId,
          this.streamingResponse,
          MessageStatus.COMPLETED,
          false
        );
        console.log(
          `🎯 Completed streaming message due to speech interruption: ${this.currentStreamingMessageId}`
        );
      }

      // 重置流式状态，为新的AI响应做准备
      this.isStreamingResponse = false;
      this.currentStreamingMessageId = null;
      this.streamingResponse = "";
    }

    this.addDebugMessage("🎤 用户开始说话，音频播放已中断");
  }

  /**
   * Handle speech stopped event
   * @param {Object} message Speech stopped message
   */
  handleSpeechStopped(message) {
    console.log("🎤 Speech stopped detected");

    // Update status
    this.updateTranscriptStatus("语音处理中...");

    this.addDebugMessage("🎤 用户停止说话，等待转录结果");
  }

  /**
   * 🎯 修复：处理来自后端的打断消息（已弃用，保留兼容性）
   * 注意：这个函数主要用于向后兼容，新的打断逻辑使用 handleControlInterruption
   */
  handleInterruptionMessage(message) {
    // 🎯 调试日志: 记录收到的消息和当前录音状态
    console.log(
      `🚨 FRONTEND: Handling legacy interruption. Status: ${message.status}, Type: ${message.interruption_type}, Current recording state: ${this.isRecording}`
    );
    this.addDebugMessage(`🚨 Legacy Interruption command: ${message.status} (${message.interruption_type})`);

    if (message.status === "start") {
      this.isInterruptedByAI = true;

      // 1. 立即停止用户录音
      if (this.isRecording) {
        console.log(
          "🔇 FRONTEND: Stopping user recording due to AI interruption."
        );
        this.audioManager.stopRecording();
      } else {
        console.log(
          "ℹ️ FRONTEND: AI interruption received, but user was not recording."
        );
      }

      // 2. 🎯 修复：区分不同的打断类型（与 handleControlInterruption 保持一致）
      if (message.interruption_type === "barge_in") {
        console.log("🛑 FRONTEND: Legacy barge-in detected, stopping current AI audio");
        this.audioManager.stopAudio();
      } else if (message.interruption_type === "disruptive") {
        console.log("🧠 FRONTEND: Legacy strategic interruption detected, AI will provide guidance");
        // 不停止音频，让AI开始说话（strategic_thinker场景）
      } else {
        console.log("🤖 FRONTEND: Legacy other interruption type detected, AI will start speaking");
        // 对于其他类型的打断，也不停止音频
      }

      // 3. 更新UI状态，告知用户AI正在回复，并禁用操作按钮
      this.updateTranscriptStatus("AI is responding...");
      this.updateConversationButton("ai-speaking");
    } else if (message.status === "end") {
      this.isInterruptedByAI = false;

      console.log("🎤 AI interruption finished. Ready for user input.");
      this.updateTranscriptStatus("Ready for your speech...");

      // 🎯 核心修复: 自动恢复录音
      // AI 打断结束后，让用户可以无缝继续说话
      this.updateConversationButton("connected"); // 首先更新UI
      if (!this.isRecording) {
        console.log(
          "▶️ Automatically resuming recording after AI interruption."
        );
        this.startRecording(); // 然后自动开始录音
      }
    }
  }

  /**
   * 🎯 修复：专门处理打断控制消息的函数
   * 区分不同类型的打断：barge_in vs proactive interruption
   */
  handleControlInterruption(message) {
    console.log(
      `🚨 FRONTEND: Handling CONTROL interruption. Status: ${message.status}, Type: ${message.interruption_type}, Current recording state: ${this.isRecording}`
    );
    this.addDebugMessage(`🚨 Control Interruption: ${message.status} (${message.interruption_type})`);

    if (message.status === "start") {
      this.isInterruptedByAI = true;

      // 1. 立即停止用户录音（对所有打断类型都适用）
      if (this.isRecording) {
        console.log(
          "🔇 FRONTEND: Stopping user recording due to AI control interruption."
        );
        this.audioManager.stopRecording();
      } else {
        console.log(
          "ℹ️ FRONTEND: AI control interruption received, but user was not recording."
        );
      }

      // 2. 🎯 修复：区分不同的打断类型
      // - barge_in: 用户打断AI，需要停止AI音频
      // - disruptive: AI主动打断用户（如strategic_thinker），AI需要开始说话，不应停止音频
      if (message.interruption_type === "barge_in") {
        console.log("🛑 FRONTEND: Barge-in detected, stopping current AI audio");
        this.audioManager.stopAudio();
      } else if (message.interruption_type === "disruptive") {
        console.log("� FRONTEND: Strategic interruption detected, AI will provide guidance");
        // 不停止音频，让AI开始说话（strategic_thinker场景）
      } else {
        console.log("🤖 FRONTEND: Other interruption type detected, AI will start speaking");
        // 对于其他类型的打断，也不停止音频
      }

      // 3. 更新UI状态，告知用户AI正在回复，并禁用操作按钮
      this.updateTranscriptStatus("AI is responding...");
      this.updateConversationButton("ai-speaking");

      // 4. 如果消息包含持续时间，记录并显示
      if (message.duration) {
        console.log(
          `⏳ FRONTEND: Control interruption will last ${message.duration} seconds`
        );
      }
    } else if (message.status === "end") {
      this.isInterruptedByAI = false;
      console.log("🎤 AI control interruption finished. Ready for user input.");
      this.updateTranscriptStatus("Ready for your speech...");

      // 🎯 核心修复: 自动恢复录音
      // AI打断保护期结束后，让用户可以无缝继续说话
      this.updateConversationButton("connected"); // 首先更新UI
      if (!this.isRecording) {
        console.log(
          "▶️ Automatically resuming recording after AI control interruption."
        );
        this.startRecording(); // 然后自动开始录音
      }
    }
  }

  /**
   * 🎯 新增：处理合作型附和的函数
   */
  handleCooperativeInterjection(message) {
    const text = message.content || message.text || "";
    if (!text) return;

    console.log(`🤝 Received cooperative interjection: ${text}`);
    this.addDebugMessage(`🤝 Received interjection: ${text}`);

    // --- START OF FIX: 创建一个独立的浮动UI元素 ---
    try {
      const chatMessages = this.elements.chatMessages;
      const lastMessageElement = chatMessages.querySelector(
        ".chat-message:last-child"
      );

      if (!lastMessageElement) return;

      // 移除任何旧的附和
      const oldInterjection = lastMessageElement.querySelector(
        ".cooperative-interjection"
      );
      if (oldInterjection) {
        oldInterjection.remove();
      }

      // 创建新的附和元素
      const interjectionEl = document.createElement("div");
      interjectionEl.className = "cooperative-interjection";
      interjectionEl.textContent = text;

      // 把它添加到最后一个消息元素里
      lastMessageElement.appendChild(interjectionEl);

      // 动画效果
      setTimeout(() => {
        interjectionEl.classList.add("visible");
      }, 10);

      // 2秒后自动消失
      setTimeout(() => {
        interjectionEl.classList.remove("visible");
        setTimeout(() => interjectionEl.remove(), 300); // 在动画结束后移除DOM
      }, 2000);
    } catch (error) {
      console.error("Error displaying cooperative interjection:", error);
    }
    // --- END OF FIX ---
  }

  /**
   * 🎯 第三种方案：处理音频控制消息
   * @param {Object} message 音频控制消息
   */
  handleAudioControl(message) {
    const action = message.action;
    const reason = message.reason || "";
    const duration = message.duration || 5.0;
    const userMessage = message.message || "";

    console.log(`🎤 FRONTEND: Audio control action: ${action}, reason: ${reason}`);
    this.addDebugMessage(`🎤 Audio control: ${action} (${reason})`);

    switch (action) {
      case "stop_recording":
        this.handleStopRecordingCommand(reason, duration, userMessage);
        break;
      case "resume_recording":
        this.handleResumeRecordingCommand(userMessage);
        break;
      default:
        console.warn(`🎤 FRONTEND: Unknown audio control action: ${action}`);
    }
  }

  /**
   * 处理停止录音命令
   */
  handleStopRecordingCommand(reason, duration, userMessage) {
    console.log(`🛑 FRONTEND: Stopping recording for ${duration}s due to: ${reason}`);

    // 1. 立即停止录音
    if (this.isRecording) {
      this.audioManager.stopRecording();
      this.isRecording = false;
      this.updateRecordingStatus(false);
      console.log("🛑 Recording stopped immediately");
    }

    // 2. 清空音频缓冲区
    if (this.audioManager.clearAudioBuffer) {
      this.audioManager.clearAudioBuffer();
      console.log("🗑️ Audio buffer cleared");
    }

    // 3. 显示用户提示
    if (userMessage) {
      this.showTemporaryMessage(userMessage, duration * 1000);
    }

    // 4. 禁用录音按钮
    this.setRecordingEnabled(false);

    // 5. 设置定时器自动恢复（备用机制）
    this.audioControlTimeout = setTimeout(() => {
      console.log("🔄 Auto-resuming recording after timeout");
      this.handleResumeRecordingCommand("Recording automatically resumed");
    }, duration * 1000);

    this.addDebugMessage(`🛑 Recording disabled for ${duration}s: ${reason}`);
  }

  /**
   * 处理恢复录音命令
   */
  handleResumeRecordingCommand(userMessage) {
    console.log("✅ FRONTEND: Resuming recording capability");

    // 1. 清除自动恢复定时器
    if (this.audioControlTimeout) {
      clearTimeout(this.audioControlTimeout);
      this.audioControlTimeout = null;
    }

    // 2. 启用录音按钮
    this.setRecordingEnabled(true);

    // 3. 显示用户提示
    if (userMessage) {
      this.showTemporaryMessage(userMessage, 2000);
    }

    this.addDebugMessage("✅ Recording capability restored");
  }

  /**
   * 设置录音功能启用/禁用状态
   */
  setRecordingEnabled(enabled) {
    const recordButton = this.elements.recordButton;
    if (recordButton) {
      recordButton.disabled = !enabled;
      if (enabled) {
        recordButton.classList.remove("disabled");
        recordButton.title = "Click to start recording";
      } else {
        recordButton.classList.add("disabled");
        recordButton.title = "Recording temporarily disabled";
      }
    }
  }

  /**
   * 显示临时消息
   */
  showTemporaryMessage(message, duration) {
    // 创建临时消息元素
    const messageEl = document.createElement("div");
    messageEl.className = "temporary-message";
    messageEl.textContent = message;

    // 添加到页面
    document.body.appendChild(messageEl);

    // 显示动画
    setTimeout(() => messageEl.classList.add("visible"), 10);

    // 自动移除
    setTimeout(() => {
      messageEl.classList.remove("visible");
      setTimeout(() => messageEl.remove(), 300);
    }, duration);
  }

  /**
   * Handle connection quality changes
   * @param {string} newQuality New connection quality
   * @param {string} oldQuality Previous connection quality
   */
  handleConnectionQualityChange(newQuality, oldQuality) {
    console.log(`🌐 Connection quality changed: ${oldQuality} → ${newQuality}`);

    // Update UI based on connection quality
    const statusElement = this.elements.statusIndicator;
    if (statusElement) {
      // Remove old quality classes
      statusElement.classList.remove(
        "connected",
        "connecting",
        "disconnected",
        "poor",
        "unstable"
      );

      // Add new quality class
      switch (newQuality) {
        case "good":
          statusElement.classList.add("connected");
          break;
        case "poor":
        case "unstable":
          statusElement.classList.add("connecting");
          break;
        case "disconnected":
        case "failed":
          statusElement.classList.add("disconnected");
          break;
        default:
          statusElement.classList.add("connecting");
      }
    }

    // Update status text
    if (this.elements.statusText) {
      const qualityText = {
        good: "已连接",
        poor: "连接不稳定",
        unstable: "连接质量差",
        reconnecting: "重连中...",
        disconnected: "已断开",
        failed: "连接失败",
      };

      this.elements.statusText.textContent =
        qualityText[newQuality] || "未知状态";
    }

    this.addDebugMessage(`🌐 连接质量变化: ${oldQuality} → ${newQuality}`);
  }

  /**
   * 确保音频上下文已恢复（需要用户交互）
   */
  async ensureAudioContextResumed() {
    if (this.audioManager.audioContext) {
      if (this.audioManager.audioContext.state === "suspended") {
        console.log("🎵 Audio context is suspended, attempting to resume...");
        try {
          await this.audioManager.audioContext.resume();
          console.log("🎵 Audio context resumed successfully");
        } catch (error) {
          console.error("Failed to resume audio context:", error);
          throw error;
        }
      }
    }
  }

  /**
   * 添加用户交互触发音频上下文恢复
   */
  async connect() {
    try {
      this.clearErrors();
      this.updateConversationButton("connecting");
      this.addDebugMessage(`正在连接到 ${this.serverUrl}...`);

      // 在连接时尝试恢复音频上下文
      await this.ensureAudioContextResumed();

      await this.wsManager.connect(this.serverUrl);
    } catch (error) {
      this.updateConversationButton("disconnected");
      this.showError("连接失败: " + error.message);
    }
  }

  async startRecording() {
    try {
      if (!this.isConnected) {
        this.showError("请先连接到服务器");
        return;
      }

      // 🎯 如果被AI打断，则阻止用户手动开始录音
      if (this.isInterruptedByAI) {
        this.addDebugMessage("Recording blocked due to AI interruption.");
        return;
      }

      // 在开始录音时尝试恢复音频上下文
      await this.ensureAudioContextResumed();

      const success = await this.audioManager.startRecording();
      if (!success) {
        this.showError("录音启动失败");
      }
      console.log("🎤 Recording started 666");
    } catch (error) {
      this.showError("录音错误: " + error.message);
    }
  }

  async stopRecording() {
    try {
      await this.audioManager.stopRecording();
      console.log("🎤 Recording stopped 666");

      // 🎯 修复：手动停止录音时，通知后端强制结束语音检测
      // 这解决了用户说完话立即点击停止时，VAD没有时间检测到语音结束的问题
      if (this.wsManager && this.wsManager.isConnected) {
        console.log("🎯 Notifying backend that user manually stopped speaking");
        console.log(`🎯 DEBUG: WebSocket connected: ${this.wsManager.isConnected}, Session ID: ${this.wsManager.sessionId}`);

        const message = {
          type: "manual_speech_end",
          session_id: this.wsManager.sessionId,
          timestamp: new Date().toISOString(),
          reason: "user_manual_stop"
        };

        console.log("🎯 DEBUG: Sending manual_speech_end message:", message);
        this.wsManager.send(message);
      } else {
        console.log("🎯 DEBUG: Cannot send manual_speech_end - WebSocket not connected or manager missing");
        console.log(`🎯 DEBUG: wsManager exists: ${!!this.wsManager}, isConnected: ${this.wsManager?.isConnected}`);
      }
    } catch (error) {
      this.showError("Failed to stop recording: " + error.message);
    }
  }

  handleModeSelection(event) {
    const button = event.target;
    const mode = button.getAttribute("data-mode");

    console.log(`🎯 模式选择事件: button=${button.id}, mode=${mode}`);
    this.addDebugMessage(`用户选择模式: ${mode}`);

    // 检查模式值是否有效
    if (!mode) {
      console.error("❌ 模式选择失败: 按钮缺少 data-mode 属性", button);
      this.addDebugMessage("❌ 模式选择失败: 按钮缺少 data-mode 属性");
      this.showError("模式选择失败，请刷新页面重试");
      return;
    }

    // 更新UI状态
    document.querySelectorAll(".mode-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    button.classList.add("active");

    // 保存选择的模式
    this.selectedMode = mode;

    // 🎯 新增：同步模式到聊天管理器
    if (this.chatManager) {
      this.chatManager.setMode(mode);
      console.log(`📋 聊天显示模式已切换到: ${mode}`);
    }

    // 选择模式后，如果已连接则启用录音按钮
    if (this.isConnected) {
      this.updateConversationButton("connected");
    }

    // 发送模式选择到服务器
    this.sendModeSelection(mode);
  }

  sendModeSelection(mode) {
    console.log(`🎯 发送模式选择: mode=${mode}, connected=${this.isConnected}, sessionId=${this.sessionId}`);

    if (!mode) {
      console.error("❌ 无法发送模式选择: 模式值为空");
      this.addDebugMessage("❌ 无法发送模式选择: 模式值为空");
      return;
    }

    if (!this.isConnected) {
      this.addDebugMessage("未连接服务器，模式选择将在连接后发送");
      // 可以存储模式选择，连接后发送
      this.pendingMode = mode;
      return;
    }

    const message = {
      type: "mode_selection",
      mode: mode,
      session_id: this.sessionId,
      timestamp: new Date().toISOString(),
    };

    console.log(`🎯 发送模式选择消息:`, message);
    this.wsManager.send(message);
    this.addDebugMessage(`✅ 模式选择已发送: ${mode}`);

    // 显示模式切换确认
    this.showModeChangeConfirmation(mode);
  }

  showModeChangeConfirmation(mode) {
    const modeNames = {
      standard: "标准助手",
      interviewer: "面试官模式",
    };

    const modeName = modeNames[mode] || mode;

    // 可以在这里添加一个临时通知或更新状态显示
    this.addToConversation("system", `🎯 已切换到${modeName}`);
  }

  handleTranscriptMessage(message) {
    const text = message.text || message.content || "";
    const source = message.source || "unknown";
    const timestamp = Date.now();

    if (message.type === "transcript_delta") {
      // 🎯 改进：更智能的流式转录显示逻辑
      this.handleTranscriptDelta(text, source, timestamp);
    } else if (message.type === "transcript_complete") {
      // 🎯 改进：智能转录缓冲和时序协调机制
      this.handleTranscriptComplete(text, source, timestamp);
    }
  }

  /**
   * 🎯 新增：处理转录增量消息的专用方法
   * @param {string} text 转录文本
   * @param {string} source 转录源
   * @param {number} timestamp 时间戳
   */
  handleTranscriptDelta(text, source, timestamp) {
    // 1. 评估转录质量
    const quality = this.evaluateTranscriptQuality(text, source);

    // 2. 决定是否应该显示这个delta
    const shouldDisplay = this.shouldDisplayTranscriptDelta(text, source, quality);

    if (shouldDisplay) {
      // 3. 更新当前转录状态
      this.currentTranscriptSource = source;
      this.currentTranscriptText = text;
      this.transcriptBuffer.qualityScore[source] = quality;

      // 4. 使用改进的流式显示
      this.updateTranscriptStatusSafe(text, true, source);
      this.addDebugMessage(`User transcript delta (${source}, quality: ${quality.toFixed(2)}): ${text}`);
    } else {
      this.addDebugMessage(`User transcript delta ignored (${source}, quality: ${quality.toFixed(2)}): ${text} - current source: ${this.currentTranscriptSource}`);
    }
  }

  /**
   * 🎯 新增：评估转录质量
   * @param {string} text 转录文本
   * @param {string} source 转录源
   * @returns {number} 质量分数 (0-1)
   */
  evaluateTranscriptQuality(text, source) {
    let score = 0.5; // 基础分数

    // 1. 基于转录源的基础质量
    if (source === "paraformer") {
      score = 0.8; // paraformer 基础质量更高
    } else if (source === "qwen") {
      score = 0.6; // qwen 作为备选
    }

    // 2. 基于文本长度和完整性
    if (text && text.length > 0) {
      score += Math.min(text.length / 50, 0.2); // 长度奖励，最多0.2
    }

    // 3. 基于文本质量（简单启发式）
    if (text) {
      // 检查是否包含完整的词汇
      const wordCount = text.trim().split(/\s+/).length;
      if (wordCount >= 2) score += 0.1;

      // 检查是否有标点符号（表示句子完整性）
      if (/[。！？，、；：]/.test(text)) score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 🎯 新增：判断是否应该显示转录增量
   * @param {string} text 转录文本
   * @param {string} source 转录源
   * @param {number} quality 质量分数
   * @returns {boolean} 是否应该显示
   */
  shouldDisplayTranscriptDelta(text, source, quality) {
    // 1. 如果没有当前源，或者新源质量更高，则显示
    if (!this.currentTranscriptSource) {
      return true;
    }

    // 2. 如果是相同源的更新，总是显示
    if (source === this.currentTranscriptSource) {
      return true;
    }

    // 3. 如果新源是paraformer，且当前是qwen，则切换
    if (source === "paraformer" && this.currentTranscriptSource === "qwen") {
      return true;
    }

    // 4. 基于质量分数决定
    const currentQuality = this.transcriptBuffer.qualityScore[this.currentTranscriptSource] || 0;
    return quality > currentQuality + 0.1; // 需要明显更好才切换
  }

  /**
   * 🎯 改进：智能处理转录完成事件，解决paraformer延迟问题
   */
  handleTranscriptComplete(text, source, timestamp) {
    this.addDebugMessage(`Transcript complete received: ${source} - "${text}"`);

    // 🎯 改进：防止重复处理相同文本
    if (this.transcriptBuffer.lastProcessedText === text && text.length > 0) {
      this.addDebugMessage(`🔄 重复转录被忽略: ${source} - "${text}"`);
      return;
    }

    // 将转录结果存入缓冲区
    this.transcriptBuffer[source] = text;
    this.transcriptBuffer[`${source}Timestamp`] = timestamp;

    // 🎯 改进：基于质量和时序的智能选择
    const quality = this.evaluateTranscriptQuality(text, source);
    this.transcriptBuffer.qualityScore[source] = quality;

    if (source === "paraformer") {
      // paraformer结果到达，优先使用
      this.processTranscriptResultImproved(text, source, "priority", quality);
      this.clearTranscriptBuffer();
      this.processQueuedAIResponses(); // 处理等待中的AI响应
    } else if (source === "qwen") {
      // qwen结果到达，智能决策是否等待paraformer
      if (this.shouldWaitForParaformerImproved(quality)) {
        this.startWaitingForParaformerImproved(text, timestamp, quality);
      } else {
        // 不需要等待，直接使用qwen结果
        this.processTranscriptResultImproved(text, source, "immediate", quality);
        this.processQueuedAIResponses();
      }
    }
  }

  /**
   * 🎯 改进：智能判断是否应该等待paraformer结果
   * @param {number} qwenQuality qwen转录质量分数
   * @returns {boolean} 是否应该等待
   */
  shouldWaitForParaformerImproved(qwenQuality) {
    // 1. 如果已经有paraformer结果，不需要等待
    if (this.transcriptBuffer.paraformer) {
      return false;
    }

    // 2. 如果当前正在等待，不重复等待
    if (this.transcriptBuffer.waitingForParaformer) {
      return false;
    }

    // 3. 如果qwen质量已经很高，不需要等待
    if (qwenQuality >= 0.9) {
      this.addDebugMessage(`🎯 Qwen质量很高(${qwenQuality.toFixed(2)})，不等待paraformer`);
      return false;
    }

    // 4. 如果用户正在快速说话（连续转录），优先响应性
    const now = Date.now();
    const lastQwenTime = this.transcriptBuffer.qwenTimestamp || 0;
    if (now - lastQwenTime < 500) { // 500ms内的快速更新
      this.addDebugMessage(`🎯 快速转录模式，不等待paraformer`);
      return false;
    }

    // 5. 默认等待paraformer结果（因为质量更好）
    return true;
  }

  /**
   * 🎯 保留：向后兼容的等待判断方法
   */
  shouldWaitForParaformer() {
    return this.shouldWaitForParaformerImproved(0.5); // 使用默认质量分数
  }

  /**
   * 🎯 改进：开始等待paraformer结果
   * @param {string} qwenText qwen转录文本
   * @param {number} qwenTimestamp qwen时间戳
   * @param {number} qwenQuality qwen质量分数
   */
  startWaitingForParaformerImproved(qwenText, qwenTimestamp, qwenQuality) {
    this.transcriptBuffer.waitingForParaformer = true;
    this.addDebugMessage(`Waiting for paraformer result (qwen ready: "${qwenText}", quality: ${qwenQuality.toFixed(2)})`);

    // 🎯 改进：基于质量动态调整等待时间
    let waitTime = this.transcriptBuffer.maxWaitTime;
    if (qwenQuality >= 0.8) {
      waitTime = Math.min(waitTime, 800); // 高质量时减少等待
    } else if (qwenQuality <= 0.4) {
      waitTime = Math.max(waitTime, 2000); // 低质量时增加等待
    }

    // 设置超时，如果paraformer太慢就使用qwen结果
    this.transcriptBuffer.timeoutId = setTimeout(() => {
      if (this.transcriptBuffer.waitingForParaformer) {
        this.addDebugMessage(`Paraformer timeout (${waitTime}ms), using qwen fallback: "${qwenText}"`);
        this.processTranscriptResultImproved(qwenText, "qwen", "timeout_fallback", qwenQuality);
        this.clearTranscriptBuffer();
        this.processQueuedAIResponses();
      }
    }, waitTime);
  }

  /**
   * 🎯 保留：向后兼容的等待方法
   */
  startWaitingForParaformer(qwenText, qwenTimestamp) {
    this.startWaitingForParaformerImproved(qwenText, qwenTimestamp, 0.6);
  }

  /**
   * 🎯 改进：处理转录结果并显示到界面
   * @param {string} text 转录文本
   * @param {string} source 转录源
   * @param {string} reason 处理原因
   * @param {number} quality 质量分数
   */
  processTranscriptResultImproved(text, source, reason, quality) {
    // 🎯 改进：防止处理空文本或重复文本
    if (!text || text.trim().length === 0) {
      this.addDebugMessage(`🎯 空转录文本被忽略 (${source} - ${reason})`);
      return;
    }

    if (this.transcriptBuffer.lastProcessedText === text) {
      this.addDebugMessage(`🎯 重复转录文本被忽略 (${source} - ${reason}): "${text}"`);
      return;
    }

    // 🎯 改进：记录处理状态
    this.transcriptBuffer.lastProcessedText = text;
    this.currentTranscriptText = text;

    // 🎯 改进：添加到聊天界面
    const message = this.addChatMessage(MessageType.USER, text);
    if (message) {
      // 为消息添加质量和源信息（用于调试）
      message.metadata = {
        source: source,
        quality: quality,
        reason: reason,
        timestamp: Date.now()
      };
    }

    // 🎯 改进：清理转录状态
    this.clearTranscriptStatusSafe();
    this.currentTranscriptSource = null;

    this.addDebugMessage(`🎯 用户转录已处理 (${source} - ${reason}, 质量: ${quality.toFixed(2)}): "${text}"`);
  }

  /**
   * 🎯 保留：向后兼容的转录结果处理方法
   */
  processTranscriptResult(text, source, reason) {
    this.processTranscriptResultImproved(text, source, reason, 0.5);
  }

  /**
   * 🎯 新增：安全的转录状态清理方法
   */
  clearTranscriptStatusSafe() {
    try {
      if (this.elements.transcriptStatus) {
        // 清除流式光标
        this.clearStreamingCursor();

        // 重置为默认状态
        this.elements.transcriptStatus.textContent = "等待语音输入...";
        this.elements.transcriptStatus.classList.remove("active");

        this.addDebugMessage("🎯 转录状态已安全清理");
      }
    } catch (error) {
      console.error("🎯 转录状态清理失败:", error);
    }
  }

  /**
   * 🎯 改进：清理转录缓冲区
   */
  clearTranscriptBuffer() {
    // 1. 清理定时器
    if (this.transcriptBuffer.timeoutId) {
      clearTimeout(this.transcriptBuffer.timeoutId);
      this.transcriptBuffer.timeoutId = null;
    }

    // 2. 清理缓存数据
    this.transcriptBuffer.qwen = null;
    this.transcriptBuffer.paraformer = null;
    this.transcriptBuffer.qwenTimestamp = null;
    this.transcriptBuffer.paraformerTimestamp = null;
    this.transcriptBuffer.waitingForParaformer = false;

    // 🎯 改进：重置质量分数和状态
    this.transcriptBuffer.qualityScore.qwen = 0;
    this.transcriptBuffer.qualityScore.paraformer = 0;

    // 🎯 改进：不清理lastProcessedText，用于防止重复处理
    // this.transcriptBuffer.lastProcessedText = ""; // 保留用于去重

    this.addDebugMessage("🎯 转录缓冲区已清理");
  }

  /**
   * 处理等待中的AI响应队列
   */
  processQueuedAIResponses() {
    if (this.transcriptBuffer.pendingAIResponses.length > 0) {
      this.addDebugMessage(`Processing ${this.transcriptBuffer.pendingAIResponses.length} queued AI responses`);

      // 按时间戳排序并逐个处理
      this.transcriptBuffer.pendingAIResponses
        .sort((a, b) => a.timestamp - b.timestamp)
        .forEach(response => {
          this.handleAIResponseMessage(response.message);
        });

      this.transcriptBuffer.pendingAIResponses = [];
    }
  }

  handleAIResponseMessage(message) {
    const text = message.text || message.content || "";
    const messageKey = message.message_key || null;

    // 🎯 新增：检查是否需要将AI响应加入队列等待用户转录完成
    if (this.transcriptBuffer.waitingForParaformer) {
      this.addDebugMessage(`Queueing AI response while waiting for paraformer: "${text.substring(0, 30)}..."`);
      this.transcriptBuffer.pendingAIResponses.push({
        message: message,
        timestamp: Date.now()
      });
      return; // 暂时不处理，等待转录完成后再处理
    }

    console.log(`🤖 AI Response received:`, {
      type: message.type,
      text: text.substring(0, 50) + (text.length > 50 ? "..." : ""),
      complete: message.complete,
      streaming: message.streaming,
      messageKey: messageKey,
      currentStreamingId: this.currentStreamingMessageId,
      isStreamingResponse: this.isStreamingResponse,
      isRecording: this.isRecording, // 🎯 添加录音状态调试
    });

    // 🎯 调试：检查当前录音状态对消息处理的影响
    if (!this.isRecording) {
      console.log(`🎯 DEBUG: Received AI response while NOT recording. This might be the issue!`);

      // 🎯 调试：检查DOM和聊天管理器状态
      if (this.chatManager && this.elements.chatMessages) {
        console.log(`🎯 DEBUG: DOM state - container exists: ${!!this.elements.chatMessages}, visible: ${this.elements.chatMessages.offsetHeight > 0}, current messages: ${this.chatManager.messages.size}, container children: ${this.elements.chatMessages.children.length}`);
      } else {
        console.log(`🎯 DEBUG: Chat manager or container missing! chatManager: ${!!this.chatManager}, container: ${!!this.elements.chatMessages}`);
      }
    }

    // 🎯 彻底修复：统一AI响应处理逻辑，杜绝重复消息问题
    if (message.type === "ai_response") {
      // 对于没有文本内容的消息，只处理状态更新
      if (!text) {
        console.log(`🤖 Empty AI response, complete=${message.complete}`);
        if (message.complete) {
          this.clearAIStatus();
          // 确保即使没有文本，完成状态也能重置流式标志
          this.isStreamingResponse = false;
          this.currentStreamingMessageId = null;
          this.streamingResponse = "";
          console.log(`🤖 Reset streaming state due to empty complete message`);
        }
        return;
      }

      // 核心逻辑：判断是创建新消息还是更新旧消息
      if (!this.isStreamingResponse) {
        // 这是一轮新对话的开始 - 立即设置状态锁，防止后续消息重复创建
        console.log(`🤖 Starting new AI response stream`);
        this.isStreamingResponse = true;
        const status = message.complete
          ? MessageStatus.COMPLETED
          : MessageStatus.STREAMING;
        // 🎯 第一次创建消息时，如果有文本内容且未完成，则启用流式效果
        const newMessage = this.addChatMessage(
          MessageType.ASSISTANT,
          "",
          status,
          messageKey
        );
        if (newMessage) {
          this.currentStreamingMessageId = newMessage.id;
          this.streamingResponse = text;
          // 立即更新消息内容，启用流式效果
          const useStreamingEffect = !message.complete && text.length > 0;
          console.log(
            `🎯 First message: messageId=${newMessage.id}, useEffect=${useStreamingEffect}, complete=${message.complete}, textLength=${text.length}`
          );
          this.chatManager.updateMessage(
            newMessage.id,
            text,
            status,
            useStreamingEffect
          );
        }
        this.updateAIStatus("AI 正在响应...");
      } else {
        // 这是一轮对话的中间或结束 - 更新现有消息
        const oldLength = this.streamingResponse.length;
        this.streamingResponse += text; // 累加文本
        if (this.currentStreamingMessageId && this.chatManager) {
          const status = message.complete
            ? MessageStatus.COMPLETED
            : MessageStatus.STREAMING;
          // 🎯 启用流式文本效果 - 只有在有新内容且未完成时才启用
          const useStreamingEffect =
            !message.complete &&
            text.length > 0 &&
            this.streamingResponse.length > oldLength;
          console.log(
            `🎯 Streaming update: messageId=${this.currentStreamingMessageId}, useEffect=${useStreamingEffect}, complete=${message.complete}, textLength=${text.length}, totalLength=${this.streamingResponse.length}`
          );
          this.chatManager.updateMessage(
            this.currentStreamingMessageId,
            this.streamingResponse,
            status,
            useStreamingEffect
          );
        } else {
          console.warn(
            `🤖 Cannot update streaming message: currentStreamingMessageId=${
              this.currentStreamingMessageId
            }, chatManager=${!!this.chatManager}`
          );
        }
      }

      // 如果消息已完成，无论如何都要重置状态
      if (message.complete) {
        console.log(`🤖 AI response completed, resetting streaming state`);

        // 🎯 修复：如果完成消息包含文本内容，确保更新到最终消息中
        if (text && this.currentStreamingMessageId && this.chatManager) {
          console.log(
            `🎯 Final update with complete text: "${text.substring(0, 50)}..."`
          );
          this.chatManager.updateMessage(
            this.currentStreamingMessageId,
            text,
            MessageStatus.COMPLETED,
            false
          );
        }

        this.isStreamingResponse = false;
        this.currentStreamingMessageId = null;
        this.streamingResponse = "";
        this.clearAIStatus();
      }
    } else if (message.type === "ai_transcript") {
      // AI转录消息（来自音频转录增量），只更新状态，不添加到对话记录
      this.updateAIStatus(`AI: ${text}`);
      // 不调用addChatMessage，避免重复显示
    }

    this.addDebugMessage(`AI response: ${text}`);
  }

  /**
   * 添加消息到聊天界面
   * @param {string} type 消息类型 (user, assistant, system)
   * @param {string} text 消息内容
   * @param {string} status 消息状态
   * @param {string} messageKey 可选的消息键，用于去重
   * @returns {ChatMessage} 创建的消息实例
   */
  addChatMessage(
    type,
    text,
    status = MessageStatus.COMPLETED,
    messageKey = null
  ) {
    if (!this.chatManager) {
      console.warn("Chat manager not initialized");
      return null;
    }

    // 🎯 调试：记录消息添加时的状态
    console.log(`🎯 DEBUG: Adding chat message while recording=${this.isRecording}, type=${type}, text="${text.substring(0, 50)}..."`);

    const message = this.chatManager.addMessage(type, text, status, messageKey);

    // 保持向后兼容性的conversation数组（仅在消息实际添加时）
    if (message) {
      this.conversation.push({
        speaker: type,
        text: text,
        timestamp: new Date(),
      });
      this.updateStats();

      // 🎯 调试：确认消息已添加
      console.log(`🎯 DEBUG: Message successfully added with ID=${message.id}, visible messages count=${this.chatManager.messages.size}`);
    } else {
      // 🎯 调试：消息未添加的情况
      console.log(`🎯 DEBUG: Message was NOT added (possibly duplicate or filtered)`);
    }

    return message;
  }

  /**
   * 向后兼容的addToConversation方法
   * @param {string} speaker 发言者 (user, ai, system)
   * @param {string} text 消息内容
   */
  addToConversation(speaker, text) {
    // 映射旧的speaker名称到新的MessageType
    let messageType = speaker;
    if (speaker === "ai") {
      messageType = MessageType.ASSISTANT;
    } else if (speaker === "user") {
      messageType = MessageType.USER;
    } else if (speaker === "system") {
      messageType = MessageType.SYSTEM;
    }

    this.addChatMessage(messageType, text);
  }

  /**
   * 🎯 改进：安全的转录状态更新方法
   * @param {string} text 转录文本
   * @param {boolean} isStreaming 是否为流式更新
   * @param {string} source 转录源，用于调试
   */
  updateTranscriptStatusSafe(text, isStreaming = false, source = "unknown") {
    if (!this.elements.transcriptStatus) {
      console.warn("🎯 转录状态元素不存在，跳过更新");
      return;
    }

    try {
      // 🎯 改进：更安全的流式显示逻辑
      if (isStreaming && text) {
        // 1. 保存当前状态，防止意外清除
        const currentText = this.elements.transcriptStatus.textContent || "";

        // 2. 安全地清除之前的光标
        this.clearStreamingCursor();

        // 3. 使用innerHTML来精确控制内容，避免textContent的问题
        const sanitizedText = this.sanitizeText(text);
        this.elements.transcriptStatus.innerHTML =
          `${sanitizedText}<span class="streaming-cursor" style="color: var(--primary-color, #007bff); animation: blink 1s infinite;">▋</span>`;

        // 4. 记录更新状态
        this.addDebugMessage(`🎯 流式转录更新 (${source}): "${text}" (长度: ${text.length})`);
      } else {
        // 🎯 改进：最终文本设置
        this.clearStreamingCursor();
        const finalText = text || "等待语音输入...";
        this.elements.transcriptStatus.textContent = finalText;

        if (text) {
          this.addDebugMessage(`🎯 转录完成 (${source}): "${text}"`);
        }
      }

      // 🎯 改进：状态样式管理
      this.updateTranscriptStatusStyle(text);

    } catch (error) {
      console.error("🎯 转录状态更新失败:", error);
      // 降级处理：使用简单的文本更新
      this.elements.transcriptStatus.textContent = text || "等待语音输入...";
    }
  }

  /**
   * 🎯 新增：清除流式光标的安全方法
   */
  clearStreamingCursor() {
    if (!this.elements.transcriptStatus) return;

    const existingCursor = this.elements.transcriptStatus.querySelector(".streaming-cursor");
    if (existingCursor) {
      existingCursor.remove();
    }
  }

  /**
   * 🎯 新增：文本清理方法，防止XSS
   * @param {string} text 原始文本
   * @returns {string} 清理后的文本
   */
  sanitizeText(text) {
    if (!text) return "";

    // 基本的HTML转义
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#39;");
  }

  /**
   * 🎯 新增：更新转录状态样式
   * @param {string} text 转录文本
   */
  updateTranscriptStatusStyle(text) {
    if (!this.elements.transcriptStatus) return;

    // 添加活跃状态样式
    if (text && text !== "等待语音输入...") {
      this.elements.transcriptStatus.classList.add("active");
    } else {
      this.elements.transcriptStatus.classList.remove("active");
    }
  }

  /**
   * 🎯 保留：向后兼容的转录状态更新方法
   * @param {string} text 转录文本
   * @param {boolean} isStreaming 是否为流式更新
   */
  updateTranscriptStatus(text, isStreaming = false) {
    // 委托给新的安全方法
    this.updateTranscriptStatusSafe(text, isStreaming, "legacy");
  }

  /**
   * 🎯 改进：清除转录状态显示（委托给安全方法）
   */
  clearTranscriptStatus() {
    this.clearTranscriptStatusSafe();
  }

  /**
   * 更新AI状态显示
   * @param {string} text 状态文本
   */
  updateAIStatus(text) {
    if (this.elements.aiStatus) {
      this.elements.aiStatus.textContent = text || "等待AI响应...";

      // 添加活跃状态样式
      if (text && text !== "等待AI响应...") {
        this.elements.aiStatus.classList.add("active");
      } else {
        this.elements.aiStatus.classList.remove("active");
      }
    }
  }

  /**
   * 清除AI状态显示
   */
  clearAIStatus() {
    if (this.elements.aiStatus) {
      this.elements.aiStatus.textContent = "等待AI响应...";
      this.elements.aiStatus.classList.remove("active");
    }
  }

  // 向后兼容的方法
  updateCurrentTranscript(text) {
    this.updateTranscriptStatus(text);
  }

  clearCurrentTranscript() {
    this.clearTranscriptStatus();
  }

  updateAIResponse(text) {
    this.updateAIStatus(text);
  }

  clearConversation() {
    this.conversation = [];

    // 清除聊天管理器中的消息
    if (this.chatManager) {
      this.chatManager.clearMessages();
    }

    // 重置状态
    this.clearTranscriptStatus();
    this.clearAIStatus();
    this.clearErrors();

    // 重置流式响应状态
    this.streamingResponse = "";
    this.isStreamingResponse = false;
    this.currentStreamingMessageId = null;

    // 🎯 改进：重置转录源优先级状态
    this.currentTranscriptSource = null;
    this.lastParaformerTranscript = null;
    this.currentTranscriptText = "";

    // 🎯 改进：完全清理转录缓冲区
    this.clearTranscriptBuffer();
    this.transcriptBuffer.lastProcessedText = ""; // 清空对话时也清理去重缓存

    this.addDebugMessage("Conversation cleared");
  }

  updateConnectionStatus(connected) {
    // 更新header中的状态指示器
    if (this.elements.statusIndicator) {
      this.elements.statusIndicator.className = `status-indicator ${
        connected ? "connected" : "disconnected"
      }`;
    }

    if (this.elements.statusText) {
      this.elements.statusText.textContent = connected ? "已连接" : "未连接";
    }

    // 更新转录状态容器显示
    if (this.elements.transcriptStatusContainer) {
      this.elements.transcriptStatusContainer.style.display = connected
        ? "block"
        : "none";
    }

    // 更新对话按钮状态和模式选择显示
    if (connected) {
      // 连接成功后显示模式选择，按钮变为需要选择模式状态
      this.elements.modeSelection.style.display = "block";
      this.updateConversationButton("mode-required");
    } else {
      // 断开连接时隐藏模式选择
      this.elements.modeSelection.style.display = "none";
      this.updateConversationButton("disconnected");
      // 重置模式选择
      this.selectedMode = null;
      this.elements.modeStandard?.classList.remove("active");
      this.elements.modeInterviewer?.classList.remove("active");
    }
  }

  updateRecordingStatus(recording) {
    // 更新对话按钮状态
    if (recording) {
      this.updateConversationButton("recording");
    } else if (this.isConnected) {
      this.updateConversationButton("connected");
    } else {
      this.updateConversationButton("disconnected");
    }
  }

  updateSessionInfo() {
    if (this.elements.sessionInfo) {
      this.elements.sessionInfo.textContent = `Session: ${this.sessionId}`;
    }
  }

  startAudioVisualization() {
    if (!this.elements.visualizer) return;

    const canvas = this.elements.visualizer;
    const ctx = canvas.getContext("2d");

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    this.audioManager.startVisualization((dataArray) => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Create gradient
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, "#667eea");
      gradient.addColorStop(0.5, "#764ba2");
      gradient.addColorStop(1, "#f093fb");

      const barWidth = (canvas.width / dataArray.length) * 2.5;
      let x = 0;

      for (let i = 0; i < dataArray.length; i++) {
        const barHeight = (dataArray[i] / 255) * canvas.height * 0.8;
        const y = (canvas.height - barHeight) / 2;

        // Draw bar with gradient
        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, barWidth, barHeight);

        // Add glow effect
        ctx.shadowColor = "#667eea";
        ctx.shadowBlur = 10;
        ctx.fillRect(x, y, barWidth, barHeight);
        ctx.shadowBlur = 0;

        x += barWidth + 1;
      }

      // Update volume display
      this.updateVolumeDisplay();
    });

    // If visualization not started, ensure volume display updates
    if (!this.audioManager.isVisualizing) {
      this.volumeUpdateInterval = setInterval(() => {
        this.updateVolumeDisplay();
      }, 100);
    }
  }

  stopAudioVisualization() {
    this.audioManager.stopVisualization();

    // 清理音量更新定时器
    if (this.volumeUpdateInterval) {
      clearInterval(this.volumeUpdateInterval);
      this.volumeUpdateInterval = null;
    }
  }

  updateVolumeMeter() {
    const volumeBar = this.elements.volumeMeter?.querySelector(".volume-bar");
    if (!volumeBar) return;

    const volume = this.audioManager.getCurrentVolume();
    volumeBar.style.width = `${volume}%`;

    if (volume > 80) {
      volumeBar.style.backgroundColor = "#ef4444";
    } else if (volume > 50) {
      volumeBar.style.backgroundColor = "#f97316";
    } else {
      volumeBar.style.backgroundColor = "#10b981";
    }
  }

  /**
   * 更新音量显示
   */
  updateVolumeDisplay() {
    const volume = this.audioManager.getCurrentVolume();

    // 更新音量计
    this.updateVolumeMeter();

    // 更新音量文本显示
    if (this.elements.inputVolume) {
      this.elements.inputVolume.textContent = `音量: ${volume}`;
    }
  }

  /**
   * 更新音频状态显示
   */
  updateAudioStatus() {
    // 更新输入波形状态
    const inputWaveform = document.getElementById("input-waveform");
    if (inputWaveform) {
      if (this.isRecording) {
        inputWaveform.classList.add("active");
        inputWaveform.textContent = "🎤 正在录音...";
      } else {
        inputWaveform.classList.remove("active");
        inputWaveform.textContent = "Audio visualization will appear here";
      }
    }

    // 更新输出波形状态
    const outputWaveform = document.getElementById("output-waveform");
    if (outputWaveform) {
      if (this.isPlaying) {
        outputWaveform.classList.add("active");
        outputWaveform.textContent = "🔊 正在播放...";
      } else {
        outputWaveform.classList.remove("active");
        outputWaveform.textContent = "Audio visualization will appear here";
      }
    }

    // 更新输出状态
    const outputStatus = document.getElementById("output-status");
    if (outputStatus) {
      outputStatus.textContent = this.isPlaying ? "播放中" : "静音";
    }
  }

  /**
   * 更新统计面板中的连接状态
   */
  updateStats() {
    if (!this.elements.statsContainer) return;

    const userMessages = this.conversation.filter(
      (m) => m.speaker === "user"
    ).length;
    const aiMessages = this.conversation.filter(
      (m) => m.speaker === "ai"
    ).length;

    this.elements.statsContainer.innerHTML = `
            <div class="stat">
                <span class="stat-label">User Messages:</span>
                <span class="stat-value">${userMessages}</span>
            </div>
            <div class="stat">
                <span class="stat-label">AI Messages:</span>
                <span class="stat-value">${aiMessages}</span>
            </div>
            <div class="stat">
                <span class="stat-label">Session:</span>
                <span class="stat-value">${this.sessionId || "None"}</span>
            </div>
        `;
  }

  showError(message) {
    console.error("Error:", message);

    if (this.elements.errorContainer) {
      this.elements.errorContainer.innerHTML = `
                <div class="error-message">
                    <span class="error-icon">⚠️</span>
                    <span class="error-text">${message}</span>
                    <button class="error-dismiss" onclick="this.parentElement.remove()">×</button>
                </div>
            `;
      this.elements.errorContainer.style.display = "block";
    }

    this.addDebugMessage(`ERROR: ${message}`);
  }

  clearErrors() {
    if (this.elements.errorContainer) {
      this.elements.errorContainer.innerHTML = "";
      this.elements.errorContainer.style.display = "none";
    }
  }

  addDebugMessage(message) {
    if (!this.settings.showDebugLog) return;

    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;

    console.log(logMessage);

    if (this.elements.debugLog) {
      const div = document.createElement("div");
      div.textContent = logMessage;
      this.elements.debugLog.appendChild(div);
      this.elements.debugLog.scrollTop = this.elements.debugLog.scrollHeight;

      if (this.elements.debugLog.children.length > 100) {
        this.elements.debugLog.removeChild(this.elements.debugLog.firstChild);
      }
    }
  }

  toggleDebugLog() {
    if (this.elements.debugLog) {
      this.elements.debugLog.style.display = this.settings.showDebugLog
        ? "block"
        : "none";
    }
  }

  cleanup() {
    this.wsManager.disconnect();
    this.audioManager.cleanup();
  }

  /**
   * 初始化音频设备列表
   */
  async initializeAudioDevices() {
    try {
      // 获取音频设备列表
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices = devices.filter(
        (device) => device.kind === "audioinput"
      );

      if (this.elements.audioDevice) {
        // 清空现有选项
        this.elements.audioDevice.innerHTML = "";

        if (audioDevices.length === 0) {
          const option = document.createElement("option");
          option.value = "";
          option.textContent = "未检测到音频设备";
          this.elements.audioDevice.appendChild(option);
        } else {
          // 添加默认选项
          const defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.textContent = "默认音频设备";
          this.elements.audioDevice.appendChild(defaultOption);

          // 添加检测到的设备
          audioDevices.forEach((device, index) => {
            const option = document.createElement("option");
            option.value = device.deviceId;
            option.textContent = device.label || `音频设备 ${index + 1}`;
            this.elements.audioDevice.appendChild(option);
          });
        }
      }

      this.addDebugMessage(`检测到 ${audioDevices.length} 个音频输入设备`);
    } catch (error) {
      console.error("Failed to initialize audio devices:", error);
      if (this.elements.audioDevice) {
        this.elements.audioDevice.innerHTML =
          '<option value="">设备检测失败</option>';
      }
      this.addDebugMessage("音频设备检测失败: " + error.message);
    }
  }

  /**
   * 处理对话按钮点击
   */
  handleConversationButtonClick() {
    const currentState =
      this.elements.conversationBtn.getAttribute("data-state");

    switch (currentState) {
      case "disconnected":
        this.connect();
        break;
      case "mode-required":
        // 不允许点击，需要先选择模式
        this.showError("请先选择对话模式");
        break;
      case "connected":
        this.startRecording();
        break;
      case "recording":
        this.stopRecording();
        break;
      default:
        console.log("Unknown conversation button state:", currentState);
    }
  }

  /**
   * 更新对话按钮状态
   */
  updateConversationButton(state) {
    const btn = this.elements.conversationBtn;
    if (!btn) return;

    const icon = btn.querySelector(".btn-icon");
    const text = btn.querySelector(".btn-text");
    const status = btn.querySelector(".btn-status");

    btn.setAttribute("data-state", state);

    switch (state) {
      case "disconnected":
        icon.textContent = "🎙️";
        text.textContent = "开始对话";
        status.textContent = "点击连接服务器并开始对话";
        btn.disabled = false;
        break;
      case "connecting":
        icon.textContent = "⏳";
        text.textContent = "连接中";
        status.textContent = "正在连接服务器...";
        btn.disabled = true;
        break;
      case "mode-required":
        icon.textContent = "🎯";
        text.textContent = "请选择对话模式";
        status.textContent = "选择下方的对话模式后开始录音";
        btn.disabled = true;
        break;
      case "connected":
        icon.textContent = "🎤";
        text.textContent = "开始录音";
        status.textContent = "点击开始录音";
        btn.disabled = false;
        break;
      case "recording":
        icon.textContent = "🛑";
        text.textContent = "停止录音";
        status.textContent = "正在录音中，点击停止";
        btn.disabled = false;
        break;
      // 🎯 新增状态，表示AI正在打断说话
      case "ai-speaking":
        icon.textContent = "💬";
        text.textContent = "AI Responding";
        status.textContent = "Please wait for the AI to finish.";
        btn.disabled = true; // AI说话时，禁止用户操作按钮
        break;
    }
  }


}

// Initialize application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.audioAgentApp = new AudioAgentApp();
});
