这个系统的设计精髓在于**两条并行处理的流水线**：一条是用于**常规问答**的"深度思考"慢速通道，另一条是用于**实时打断**的"快速反应"高速通道。

### 整体链路图示

```mermaid
sequenceDiagram
    participant C as Client (JS)
    participant B as Backend (app.py)
    participant RM as RealtimeManager
    participant MB as MessageBus
    participant TT as TacticalThinker
    participant ST as StrategicThinker
    participant D as Decider
    participant O as Orchestrator
    participant S as Speaker

    Note over C,S: 用户正在说话...
    C->>B: 持续发送音频流
    B->>RM: send_audio_buffer_append()
    RM->>MB: publish(TRANSCRIPT_DELTA)

    Note over TT,D: --- 快速反应通道 (合作型附和) ---
    MB-->>TT: on(TRANSCRIPT_DELTA)
    TT->>TT: 上下文感知语义分析
    alt 发现合作信号
        TT->>MB: publish(UI_UPDATE)
        MB-->>B: on(UI_UPDATE)
        B->>C: WebSocket send(cooperative_interjection)
        Note right of C: 前端显示emoji文字附和<br/>如：【确实是👍】
    end

    Note over C,S: 用户停止说话...
    RM->>MB: publish(TRANSCRIPT_COMPLETED)

    Note over ST,D: --- 深度思考通道 (常规回复/干扰型打断) ---
    MB-->>ST: on(TRANSCRIPT_COMPLETED)
    ST->>ST: (后台)深度上下文分析
    alt 发现干扰信号 (例如跑题/冗长)
        ST->>MB: publish(DISRUPTIVE_ANALYSIS)
        MB-->>D: on(DISRUPTIVE_ANALYSIS)
        D->>D: 评估cooldown状态
        D->>MB: publish(INTERRUPTION_DECISION)
        MB-->>O: on(INTERRUPTION_DECISION)
        O->>S: 通过MB发送语音打断指令(speak_with_context)
        S->>RM: update_session_config() & send_text_input("")
        RM-->>C: AI打断语音 (Direct Audio Channel)
    else 常规回复
        Note over ST,O: 在当前逻辑中，常规回复<br/>由Orchestrator在用户语音结束后触发
        O->>S: 通过MB发送常规回复指令
        S->>RM: update_session_config() & send_text_input("")
        RM-->>C: AI回复语音 (Direct Audio Channel)
    end
```

---

### 详细链路步骤分解

#### 场景 A: 标准问答流程 (用户说完，AI 回答)

1.  **感知 (Perception)**

    - **步骤 1: 音频采集与传输**: 用户在前端讲话，`audio.js` 采集 PCM 音频数据，通过 WebSocket 持续发送给后端 `app.py`。
    - **步骤 2: 音频转发**: `app.py` 接收到音频块，调用 `realtime_manager.send_audio_buffer_append()`（通过 `Listener` 代理）。
    - **步骤 3: 语音转写**: 千问 API 进行实时语音识别。当检测到用户停顿（VAD），它会发回一个 `conversation.item.input_audio_transcription.completed` 事件，包含最终的完整转写结果。
    - **步骤 4: 情报发布**: `RealtimeManager` 接收到这个事件，将其标准化后，通过 `MessageBus` 发布到 `TopicNames.TRANSCRIPT_COMPLETED` 和 `USER_SPEECH_ENDED` 主题。

2.  **决策 (Decision)**

    - **步骤 5: 状态变更**: `Orchestrator` 监听到 `USER_SPEECH_ENDED`，将当前会话状态从 `USER_SPEAKING` 变为 `AI_THINKING`。
    - **步骤 6: 触发回复**: `Orchestrator` 随即向 `Speaker` 发送一个 `speak_with_context` 指令，内容通常是一个通用的提示（例如，"请根据对话历史进行回复"），或者由 `StrategicThinker` 在后台准备好的内容。

3.  **反应 (Reaction)**

    - **步骤 7: 指令注入与触发**: `Speaker` 接收到指令，调用 `speak_with_context` 方法。此方法会更新 `RealtimeManager` 中会话的 `instructions`，然后调用 `realtime_manager.send_text_input(text="")` 来触发千问 API 基于新指令生成回复。
    - **步骤 8: 合成与转写**: 千问 API 同时生成语音和对应的文本。`RealtimeManager` 会收到 `response.audio.delta` (音频) 和 `response.audio_transcript.delta` (文本) 事件。
    - **步骤 9: 文本回传**: `RealtimeManager` 将文本转写事件发布到 `RESPONSE_EVENTS`，`app.py` 订阅此事件并将 `ai_response` 消息发回前端，实现流式文本显示。
    - **步骤 10: 音频回传 (直接通道)**: `RealtimeManager` 通过其维护的**直接音频通道**，将音频数据块直接、低延迟地发送回前端 `app.js` 的 `direct_audio_output` 处理器。
    - **步骤 11: 播放音频**: 前端的 `DirectAudioPlayer` 接收并播放音频流，用户听到 AI 的回复。

---

#### 场景 B: AI 主动打断流程 (用户正在说，AI 介入)

### B1: 合作型附和 (文字显示)

1.  **感知 (Perception)**

    - **步骤 1: 音频采集与传输**: 用户的音频流发送到千问 API，同时通过 Paraformer 进行实时转录。
    - **步骤 2: 增量转写**: API 持续发回 `delta` 事件（实时识别的词语）。
    - **步骤 3: 情报发布**: `RealtimeManager` 集成 Paraformer 转录功能，将 `delta` 事件发布到 `TopicNames.TRANSCRIPT_DELTA` 主题。

2.  **决策 (Decision) - 合作型快速通道**

    - **步骤 4: 上下文感知分析**: `TacticalThinker` 订阅了 `TRANSCRIPT_DELTA`，**每个语音片段都会触发它**。它使用豆包模型进行**完整的语义理解和上下文分析**，结合 20 轮对话历史判断用户的情感状态和支持需求。
    - **步骤 5: 发布文字附和**: 如果 `TacticalThinker` 发现了合作信号，它会立刻向 `TopicNames.UI_UPDATE` 主题发布一个**文字附和建议** (`cooperative_interjection`)。

3.  **反应 (Reaction) - 文字显示**

    - **步骤 6: 前端展示**: `app.py` 的 `ui_update_callback` 接收到附和建议，直接通过 WebSocket 发送给前端。前端的 `handleCooperativeInterjection` 函数在 UI 上显示一个浮动的文字附和（如"【确实是 👍】"），**无语音合成**。

### B2: 干扰型打断 (语音介入)

1.  **感知 (Perception)** - 同 B1

2.  **决策 (Decision) - 干扰型深度通道**

    - **步骤 4: 深度流程分析**: `StrategicThinker` 同时监听 `TRANSCRIPT_DELTA` 和 `TRANSCRIPT_COMPLETED`，进行**实时流式分析**。它维护 20 轮对话历史，检测重复性、冗长性、话题偏移等模式。
    - **步骤 5: 发布干扰建议**: 如果 `StrategicThinker` 检测到需要干预的模式，它会向 `TopicNames.DISRUPTIVE_ANALYSIS` 主题发布一个**语音打断建议**，包含完整的打断话术。
    - **步骤 6: 决策仲裁与发布**: `Decider` 接收到建议，检查 cooldown 后，立即向 `TopicNames.INTERRUPTION_DECISION` 主题发布**最终的打断决策**。

3.  **反应 (Reaction) - 语音打断**

    - **步骤 7: 导演执行**: `Orchestrator` 接收到最终的打断决策，立即调用 `_execute_interruption` 方法。
    - **步骤 8: 执行中断**: `Orchestrator` 执行一系列动作：
      - **关键修复**: 调用 `realtime_manager.cancel_response()` 强制取消当前任何正在进行的 AI 响应。
      - 向 `Speaker` 发送中断指令 (`INTERRUPTION_COMMAND`)。
      - 向前端发送 `control_interruption` 消息，禁用录音并显示 AI 打断状态。
      - 在 AI 主动打断场景下，直接向 `Speaker` 发送 `speak_with_context` 指令，内容为打断话术。
    - **步骤 9 (同场景 A)**: `Speaker` 请求合成打断话术的语音，`RealtimeManager` 通过直接通道将音频发回前端播放，用户听到 AI 的主动介入。

### B3: 用户 Barge-in (用户打断 AI)

1.  **感知 (Perception)**

    - **步骤 1**: 用户在 AI 说话时开始说话。
    - **步骤 2**: `RealtimeManager` 从千问 API 接收到 `input_audio_buffer.speech_started` 事件。
    - **步骤 3**: `RealtimeManager` 将其发布到 `TopicNames.USER_SPEECH_STARTED` 主题。

2.  **决策与反应 (Decision & Reaction) - 快速通道**

    - **步骤 4**: `Orchestrator` 监听到 `USER_SPEECH_STARTED`。它检查到当前状态为 `AI_SPEAKING`，判定为 barge-in。
    - **步骤 5**: `Orchestrator` 立即调用 `_execute_interruption` 方法，执行与 B2 步骤 8 相同的**中断序列**，立即停止 AI 语音，并向前端发送 `control_interruption` 消息。
    - **步骤 6**: AI 语音停止，前端录音停止，控制权交还给用户。

---

### 🎯 新架构的关键优势

1.  **双模式并行**: 合作型文字附和和干扰型语音打断完全并行处理，互不干扰。
2.  **上下文感知**: 两个 `Thinker` 都维护完整对话历史，提供高质量的语义理解。
3.  **事件驱动**: 移除所有超时等待，实现真正的实时响应。
4.  **鲁棒的中断**: 通过 `cancel_response` 和直接前端控制，实现了可靠、低延迟的 barge-in 和主动中断。
5.  **用户体验**: 文字附和更自然，语音打断更精准。
