# Fully refactored agents/decider.py

from datetime import datetime, timedelta
from typing import Dict, Any
from loguru import logger

from core.base_agent import BaseAgent
from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import EventType, MessageType
from core.config import settings

class Decider(BaseAgent):
    """
    Decider Agent - A pure event-driven arbiter for spoken interruptions.
    Listens for analysis from the Strategic Thinker and makes the final decision
    to execute a spoken (disruptive) interruption.
    """
    
    def __init__(self):
        super().__init__("decider")
        self.last_interruption_time: Dict[str, datetime] = {}
        self.interruption_cooldown = timedelta(seconds=5)
        
        # 🎯 新增：面试官模式相关属性
        self.interviewer_mode = "disabled"  # "disabled", "standard", "strict"
        self.interviewer_config = {
            "max_speaking_time": 30,
            "force_interrupt_after": 45,
            "cooperative_threshold": 0.4,
            "disruptive_threshold": 0.2
        }
        
        # 🎯 新增：打断阈值属性（从config.py获取默认值）
        self.cooperative_threshold = settings.cooperative_interruption_threshold
        self.disruptive_threshold = settings.disruptive_interruption_threshold

    async def initialize(self) -> bool:
        """Initializes and subscribes to disruptive analysis results."""
        try:
            await message_bus.connect()
            await message_bus.subscribe(TopicNames.DISRUPTIVE_ANALYSIS, self.handle_disruptive_analysis)
            self.logger.info("Decider initialized to arbitrate spoken interruptions.")
            return True
        except Exception as e:
            self.logger.error(f"Decider initialization failed: {e}")
            return False

    async def start(self) -> bool:
        """启动Decider agent."""
        try:
            self.logger.info("Starting Decider agent...")
            # Decider是事件驱动的，不需要额外的启动逻辑
            # 所有必要的订阅已在initialize()中完成
            
            # 设置运行状态标志
            self.is_running = True
            
            self.logger.info("Decider agent started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start Decider: {e}")
            self.is_running = False
            return False

    async def shutdown(self) -> None:
        """关闭Decider agent并清理资源."""
        try:
            self.logger.info("Shutting down Decider agent...")
            
            # 清理中断时间记录
            self.last_interruption_time.clear()
            
            self.logger.info("Decider shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Decider shutdown: {e}")

    # 🎯 新增：配置面试官模式方法
    async def configure_interviewer_mode(self, mode: str, **config_params) -> bool:
        """
        配置面试官模式
        
        Args:
            mode: 面试官模式 ("disabled", "standard", "strict")
            **config_params: 配置参数
                - max_speaking_time: 用户最大发言时间(秒)
                - force_interrupt_after: 强制打断时间(秒) 
                - cooperative_threshold: 合作型打断阈值
                - disruptive_threshold: 干扰型打断阈值
        
        Returns:
            bool: 配置是否成功
        """
        try:
            valid_modes = ["disabled", "standard", "strict"]
            if mode not in valid_modes:
                self.logger.error(f"Invalid interviewer mode: {mode}. Valid modes: {valid_modes}")
                return False
            
            # 更新模式
            old_mode = self.interviewer_mode
            self.interviewer_mode = mode
            
            # 更新配置参数
            for key, value in config_params.items():
                if key in self.interviewer_config:
                    self.interviewer_config[key] = value
                    self.logger.debug(f"Updated {key} to {value}")
            
            # 根据模式调整打断阈值
            if mode == "standard":
                self.cooperative_threshold = self.interviewer_config.get("cooperative_threshold", 0.65)
                self.disruptive_threshold = self.interviewer_config.get("disruptive_threshold", 0.6)
            elif mode == "strict":
                self.cooperative_threshold = 0.65  # 更容易触发合作型打断
                self.disruptive_threshold = 0.5   # 更容易触发干扰型打断
            else:  # disabled
                self.cooperative_threshold = settings.cooperative_interruption_threshold
                self.disruptive_threshold = settings.disruptive_interruption_threshold
            
            self.logger.info(f"✅ Interviewer mode configured: {old_mode} -> {mode}")
            self.logger.info(f"   📊 Cooperative threshold: {self.cooperative_threshold}")
            self.logger.info(f"   📊 Disruptive threshold: {self.disruptive_threshold}")
            self.logger.info(f"   ⏱️ Max speaking time: {self.interviewer_config['max_speaking_time']}s")
            self.logger.info(f"   🚨 Force interrupt after: {self.interviewer_config['force_interrupt_after']}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to configure interviewer mode: {e}")
            return False

    def get_interviewer_status(self) -> Dict[str, Any]:
        """
        获取面试官模式状态
        
        Returns:
            Dict: 面试官模式状态信息
        """
        try:
            return {
                "interviewer_mode": self.interviewer_mode,
                "config": self.interviewer_config.copy(),
                "thresholds": {
                    "cooperative": self.cooperative_threshold,
                    "disruptive": self.disruptive_threshold
                },
                "is_enabled": self.interviewer_mode != "disabled",
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting interviewer status: {e}")
            return {
                "interviewer_mode": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _is_in_cooldown(self, session_id: str) -> bool:
        """Checks if the session is currently in a cooldown period."""
        last_time = self.last_interruption_time.get(session_id)
        if last_time and (datetime.now() - last_time) < self.interruption_cooldown:
            self.logger.debug(f"Session {session_id} is in interruption cooldown.")
            return True
        return False

    async def handle_disruptive_analysis(self, topic: str, message: Dict[str, Any]):
        """
        Handles a disruptive analysis result from the Strategic Thinker and executes it if not in cooldown.
        """
        try:
            data = message.get("data", {})
            session_id = data.get("session_id")
            analysis = data.get("analysis", {})
            
            if not session_id or not analysis or self._is_in_cooldown(session_id):
                return
            
            self.logger.info(f"Received disruptive analysis for session {session_id}. Reason: {analysis.get('reason')}")

            # 🎯 增强：考虑面试官模式的决策逻辑
            confidence = analysis.get("confidence", 0.0)
            
            # 在面试官模式下，调整置信度阈值
            effective_threshold = self.disruptive_threshold
            if self.interviewer_mode != "disabled":
                # 面试官模式下更容易触发打断
                if self.interviewer_mode == "strict":
                    effective_threshold *= 0.8  # 降低20%阈值
                else:  # standard
                    effective_threshold *= 0.9  # 降低10%阈值
                    
                self.logger.debug(f"Interviewer mode active: {self.interviewer_mode}, adjusted threshold: {effective_threshold}")

            # 检查是否满足打断条件
            if confidence > effective_threshold:
                # The decision to interrupt is already made by the Thinker.
                # Here we just format it for the Orchestrator and send it.
                decision = {
                    "type": analysis.get("interruption_type", "disruptive"),
                    "timing": "immediate", # All disruptive interruptions are immediate
                    "reason": analysis.get("reason"),
                    "confidence": confidence,
                    "suggested_response": analysis.get("suggested_response"),
                    "interviewer_mode": self.interviewer_mode,
                    "threshold_used": effective_threshold
                }

                # Publish the final, executable interruption decision for the Orchestrator
                await message_bus.publish(
                    TopicNames.INTERRUPTION_DECISION,
                    create_message(
                        event_type=EventType.INTERRUPTION_DECISION,
                        message_type=MessageType.INTERRUPTION_SIGNAL,
                        data={"session_id": session_id, "decision": decision}
                    )
                )

                # Record the interruption time to enforce cooldown
                self.last_interruption_time[session_id] = datetime.now()
                self.logger.info(f"🛑 Spoken interruption decision forwarded to Orchestrator for session {session_id} (mode: {self.interviewer_mode}).")
            else:
                self.logger.debug(f"Confidence {confidence} below threshold {effective_threshold}, no interruption triggered.")

        except Exception as e:
            self.logger.error(f"Error handling disruptive analysis: {e}") 