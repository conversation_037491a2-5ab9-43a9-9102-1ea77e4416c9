/**
 * WebSocket Manager for Audio Agent Client
 * Handles real-time communication with the audio agent server
 */

class WebSocketManager {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 1000;
    this.sessionId = null;

    // Enhanced connection management
    this.connectionQuality = "good"; // good, poor, unstable
    this.lastPingTime = null;
    this.pingInterval = null;
    this.connectionTimeout = null;
    this.heartbeatInterval = 30000; // 30 seconds

    // Audio chunk reassembly - 增加超时时间
    this.audioChunks = new Map();
    this.audioChunkTimeout = 10000; // 增加到10秒超时

    // Event handlers
    this.onConnectionChange = null;
    this.onTranscript = null;
    this.onAIResponse = null;
    this.onAudioOutput = null;
    this.onError = null;
    this.onDebug = null;

    // Enhanced event handlers
    this.onConnectionQualityChange = null;
    this.onSpeechStarted = null;
    this.onSpeechStopped = null;
    this.onInterruption = null; // 🎯 UI类型打断消息处理器
    this.onControlInterruption = null; // 🎯 新增：专属控制打断处理器
    this.onCooperativeInterjection = null; // 🎯 新增：合作型附和消息处理器

    // Add this new event handler property
    this.onAudioPlaybackStop = null;

    // Message queue for offline messages
    this.messageQueue = [];
  }

  /**
   * Connect to the WebSocket server
   * @param {string} url - WebSocket server URL
   * @param {string} sessionId - Session ID for this connection
   */
  async connect(url = "ws://localhost:8765", sessionId = null) {
    try {
      this.sessionId = sessionId || this.generateSessionId();
      const wsUrl = `${url}?session_id=${this.sessionId}`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log("🔗 WebSocket connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.connectionQuality = "good";

        // Start heartbeat
        this.startHeartbeat();

        this.onConnectionChange?.(true);
        this.processMessageQueue();
      };

      this.ws.onmessage = (event) => {
        this.handleMessage(event.data);
      };

      this.ws.onclose = (event) => {
        console.log("❌ WebSocket disconnected:", event.code, event.reason);
        this.isConnected = false;
        this.connectionQuality = "disconnected";

        // Stop heartbeat
        this.stopHeartbeat();

        this.onConnectionChange?.(false);

        if (event.code !== 1000) {
          // Not normal closure - attempt reconnection
          this.attemptReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error("🚨 WebSocket error:", error);
        this.connectionQuality = "error";
        this.onError?.("WebSocket connection error");
      };
    } catch (error) {
      console.error("Failed to connect to WebSocket:", error);
      this.onError?.("Failed to connect to server");
    }
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    this.pingInterval = setInterval(() => {
      if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
        this.sendPing();
      }
    }, this.heartbeatInterval);
  }

  /**
   * Stop heartbeat mechanism
   */
  stopHeartbeat() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  /**
   * Send ping message
   */
  sendPing() {
    if (!this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    this.lastPingTime = Date.now();
    this.send({
      type: "ping",
      timestamp: this.lastPingTime,
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    this.isConnected = false;

    // Stop heartbeat
    this.stopHeartbeat();

    // Clear connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    // Clear audio chunks
    this.audioChunks.clear();

    if (this.ws) {
      this.ws.close(1000, "Client disconnect");
      this.ws = null;
    }

    this.sessionId = null;
    this.connectionQuality = "disconnected";
  }

  /**
   * Send message to the server
   * @param {Object} message - Message to send
   */
  send(message) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(message);
      this.onDebug?.("Message queued (not connected)");
    }
  }

  /**
   * Send audio data to the server
   * @param {ArrayBuffer} audioData - Audio data to send
   */
  sendAudio(audioData) {
    if (this.isConnected && this.ws) {
      // Convert ArrayBuffer to base64
      const base64Audio = this.arrayBufferToBase64(audioData);
      this.send({
        type: "audio_input",
        data: base64Audio,
        format: "pcm16",
        sample_rate: 24000,
        session_id: this.sessionId,
      });
    }
  }

  /**
   * Request AI to start speaking
   */
  startSpeaking() {
    this.send({
      type: "start_speaking",
      session_id: this.sessionId,
    });
  }

  /**
   * Request AI to stop speaking
   */
  stopSpeaking() {
    this.send({
      type: "stop_speaking",
      session_id: this.sessionId,
    });
  }

  /**
   * Handle incoming WebSocket messages
   * @param {string} data - Raw message data
   */
  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      this.onDebug?.(`Received: ${message.type}`);

      // 🎯 调试：记录消息接收时间和类型
      console.log(`🎯 DEBUG: WebSocket message received at ${new Date().toISOString()}: ${message.type}`, message);

      switch (message.type) {
        case "transcript_delta":
        case "transcript_complete":
          this.onTranscript?.(message);
          break;

        case "ai_response":
        case "ai_transcript":
          // 🎯 修复：改进流式响应检测逻辑
          // 原逻辑有bug：短的完整回复被错误标记为streaming
          if (
            message.streaming === true ||
            (message.text &&
              !message.complete &&
              message.type === "ai_response")
          ) {
            // 明确标记为流式响应
            message.streaming = true;
            message.complete = false;
          } else if (
            message.complete === true ||
            message.type === "ai_transcript" ||
            (message.text && message.text.length > 0 && !message.streaming)
          ) {
            // 完整响应或AI转录
            message.complete = true;
            message.streaming = false;
          }
          this.onAIResponse?.(message);
          break;

        case "audio_output":
          this.onAudioOutput?.(message);
          break;

        // 🎯 新增：直接音频输出处理
        case "direct_audio_output":
          this.handleDirectAudioOutput(message);
          break;

        case "audio_output_chunk":
          this.handleAudioChunk(message);
          break;

        // Add this new case for audio playback stop
        case "audio_playback_stop":
          this.onAudioPlaybackStop?.(message);
          break;

        // 🎯 新增：处理手动语音结束确认消息
        case "manual_speech_end_ack":
          console.log("🎯 DEBUG: Received manual_speech_end_ack from backend:", message);
          break;

        case "speech_started":
          this.onSpeechStarted?.(message);
          break;

        case "speech_stopped":
          this.onSpeechStopped?.(message);
          break;

        // 🎯 新增：专属控制打断消息处理
        case "control_interruption":
          this.onControlInterruption?.(message);
          break;

        // 🎯 保留：UI类型打断消息处理
        case "interruption":
          this.onInterruption?.(message);
          break;

        // 🎯 新增：合作型附和消息处理
        case "cooperative_interjection":
          this.onCooperativeInterjection?.(message);
          break;

        // 🎯 第三种方案：音频控制消息处理
        case "audio_control":
          this.onAudioControl?.(message);
          break;

        case "connection_quality":
          this.handleConnectionQuality(message);
          break;

        case "error":
          this.onError?.(message.message || "Server error");
          break;

        case "status":
          this.handleStatusMessage(message);
          break;

        case "pong":
          this.handlePong(message);
          break;

        // 🎯 添加ping消息处理
        case "ping":
          this.handlePing(message);
          break;

        default:
          this.onDebug?.(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error("Failed to parse WebSocket message:", error);
      this.onError?.("Failed to parse server message");
    }
  }

  /**
   * 🎯 新增：处理直接音频输出消息
   * @param {Object} message Direct audio output message
   */
  handleDirectAudioOutput(message) {
    try {
      const audioData = message.data;

      console.log("🎵 Received direct audio output:", {
        format: audioData?.format,
        dataLength: audioData?.audio_data ? audioData.audio_data.length : 0,
        source: audioData?.source,
        sessionId: audioData?.session_id,
        responseId: audioData?.response_id,
        timestamp: audioData?.timestamp,
      });

      if (audioData && audioData.audio_data) {
        // 直接转发到音频输出处理器
        this.onAudioOutput?.({
          type: "audio_output",
          data: audioData.audio_data,
          format: audioData.format || "pcm16",
          sample_rate: audioData.sample_rate || 24000,
          source: "direct_channel",
          session_id: audioData.session_id,
          response_id: audioData.response_id,
          timestamp: audioData.timestamp,
        });

        this.onDebug?.(
          `🎵 Direct audio forwarded: ${audioData.audio_data.length} chars`
        );
      } else {
        console.warn("⚠️ Direct audio message missing audio data");
      }
    } catch (error) {
      console.error("❌ Error handling direct audio output:", error);
      this.onError?.("Direct audio processing failed: " + error.message);
    }
  }

  /**
   * Handle audio chunk messages for reassembly - 优化版本
   * @param {Object} message Audio chunk message
   */
  handleAudioChunk(message) {
    try {
      const { chunk_index, total_chunks, data, session_id, source } = message;
      const chunkKey = `${session_id}_${
        message.response_id || Date.now()
      }_${total_chunks}`;

      console.log("🔊 Received audio chunk:", {
        chunkIndex: chunk_index,
        totalChunks: total_chunks,
        dataLength: data ? data.length : 0,
        source: source || "unknown",
        chunkKey: chunkKey,
      });

      if (!this.audioChunks.has(chunkKey)) {
        this.audioChunks.set(chunkKey, {
          chunks: new Array(total_chunks).fill(null),
          receivedCount: 0,
          totalChunks: total_chunks,
          format: message.format || "pcm16",
          sampleRate: message.sample_rate || 24000,
          timestamp: Date.now(),
          source: source || "unknown",
        });
      }

      const audioData = this.audioChunks.get(chunkKey);

      // 防止重复分块和越界访问
      if (
        chunk_index >= 0 &&
        chunk_index < total_chunks &&
        audioData.chunks[chunk_index] === null
      ) {
        audioData.chunks[chunk_index] = data;
        audioData.receivedCount++;

        // 更新时间戳
        audioData.timestamp = Date.now();
      }

      // Check if all chunks received
      if (audioData.receivedCount === audioData.totalChunks) {
        console.log("🎵 All audio chunks received, reassembling...");

        // 重组完整音频
        const completeAudio = audioData.chunks.join("");

        this.onAudioOutput?.({
          type: "audio_output",
          data: completeAudio,
          format: audioData.format,
          sample_rate: audioData.sampleRate,
          source: `chunked_${audioData.source}`,
          reassembled: true,
          total_chunks: total_chunks,
        });

        console.log("✅ Audio reassembly completed:", {
          totalLength: completeAudio.length,
          chunks: total_chunks,
          source: audioData.source,
        });

        this.audioChunks.delete(chunkKey);
      } else {
        console.log(
          `📊 Audio chunk progress: ${audioData.receivedCount}/${audioData.totalChunks}`
        );
      }

      // Cleanup expired chunks with longer timeout
      this.cleanupExpiredChunks();
    } catch (error) {
      console.error("❌ Error handling audio chunk:", error);
      this.onError?.("Audio chunk processing failed: " + error.message);
    }
  }

  /**
   * Handle ping message from server
   * @param {Object} message Ping message
   */
  handlePing(message) {
    // Respond with pong
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.send({
        type: "pong",
        timestamp: Date.now(),
        original_timestamp: message.timestamp,
      });
      this.onDebug?.("Responded to ping with pong");
    }
  }

  /**
   * Cleanup expired audio chunks - 增强版本
   */
  cleanupExpiredChunks() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, audioData] of this.audioChunks) {
      if (now - audioData.timestamp > this.audioChunkTimeout) {
        expiredKeys.push(key);
        console.warn(
          `🗑️ Cleaning up expired audio chunk: ${key} (missing ${
            audioData.totalChunks - audioData.receivedCount
          } chunks)`
        );
      }
    }

    // 批量删除过期项
    expiredKeys.forEach((key) => {
      this.audioChunks.delete(key);
    });

    if (expiredKeys.length > 0) {
      this.onDebug?.(`Cleaned up ${expiredKeys.length} expired audio chunks`);
    }
  }

  /**
   * Handle connection quality updates
   * @param {Object} message Connection quality message
   */
  handleConnectionQuality(message) {
    const oldQuality = this.connectionQuality;
    this.connectionQuality = message.quality || "unknown";

    if (oldQuality !== this.connectionQuality) {
      this.onConnectionQualityChange?.(this.connectionQuality, oldQuality);
      this.onDebug?.(
        `Connection quality changed: ${oldQuality} → ${this.connectionQuality}`
      );
    }
  }

  /**
   * Handle pong response for ping-pong heartbeat
   * @param {Object} message Pong message
   */
  handlePong(message) {
    if (this.lastPingTime) {
      const latency = Date.now() - this.lastPingTime;
      this.onDebug?.(`Ping latency: ${latency}ms`);

      // Update connection quality based on latency
      if (latency > 1000) {
        this.connectionQuality = "poor";
      } else if (latency > 500) {
        this.connectionQuality = "unstable";
      } else {
        this.connectionQuality = "good";
      }
    }
  }

  /**
   * Handle status messages from server
   * @param {Object} message - Status message
   */
  handleStatusMessage(message) {
    this.onDebug?.(`Status: ${message.status}`);
    if (message.status === "listening_started") {
      this.onDebug?.("🎤 Server started listening");
    } else if (message.status === "listening_stopped") {
      this.onDebug?.("🔇 Server stopped listening");
    }
  }

  /**
   * Attempt to reconnect to the server with exponential backoff
   */
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;

      // Exponential backoff: 1s, 2s, 4s, 8s, 16s
      const delay = Math.min(
        this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
        30000 // Max 30 seconds
      );

      this.connectionQuality = "reconnecting";
      this.onDebug?.(
        `Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`
      );

      this.connectionTimeout = setTimeout(async () => {
        try {
          // Store the original URL for reconnection
          const originalUrl =
            this.ws?.url?.replace(/\?.*$/, "") ||
            "ws://localhost:8000/ws/conversation";
          await this.connect(originalUrl, this.sessionId);
        } catch (error) {
          console.error("Reconnection failed:", error);
          this.onError?.("Reconnection failed: " + error.message);

          // Continue attempting if we haven't reached max attempts
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
          }
        }
      }, delay);
    } else {
      this.connectionQuality = "failed";
      this.onError?.("Failed to reconnect after maximum attempts");
      this.onConnectionQualityChange?.("failed", "reconnecting");
    }
  }

  /**
   * Reset reconnection state
   */
  resetReconnectionState() {
    this.reconnectAttempts = 0;
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  /**
   * Process queued messages when connection is restored
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Generate a unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return (
      "session_" + Math.random().toString(36).substr(2, 9) + "_" + Date.now()
    );
  }

  /**
   * Convert ArrayBuffer to base64 string
   * @param {ArrayBuffer} buffer - Audio buffer
   * @returns {string} Base64 encoded string
   */
  arrayBufferToBase64(buffer) {
    const uint8Array = new Uint8Array(buffer);
    let binaryString = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binaryString);
  }

  /**
   * Convert base64 string to ArrayBuffer
   * @param {string} base64 - Base64 encoded string
   * @returns {ArrayBuffer} Audio buffer
   */
  base64ToArrayBuffer(base64) {
    const binaryString = atob(base64);
    const uint8Array = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      uint8Array[i] = binaryString.charCodeAt(i);
    }
    return uint8Array.buffer;
  }
}

// Export for use in other modules
window.WebSocketManager = WebSocketManager;
