"""
音频流分发器
负责将前端音频同时分发给千问和科大讯飞ASR服务
"""

import asyncio
from loguru import logger
from typing import Optional, TYPE_CHECKING

from .xunfei_asr_manager import XunfeiASRManager
from .config import settings

if TYPE_CHECKING:
    from .realtime_manager import RealtimeManager


class AudioStreamSplitter:
    def __init__(self):
        self.qwen_manager: Optional['RealtimeManager'] = None
        self.xunfei_manager: Optional[XunfeiASRManager] = None
        self.is_active = False
        self.current_session_id = None
        
    async def initialize(self, qwen_manager: 'RealtimeManager') -> bool:
        """初始化分发器"""
        try:
            self.qwen_manager = qwen_manager
            
            # 初始化科大讯飞管理器
            if settings.enable_dual_asr:
                self.xunfei_manager = XunfeiASRManager()
                logger.info("✅ 双轨ASR分发器初始化成功")
            else:
                logger.info("📝 单轨ASR模式（仅千问）")
            
            return True
            
        except Exception as e:
            logger.error(f"音频分发器初始化失败: {e}")
            return False
    
    async def start_session(self, session_id: str) -> bool:
        """启动音频分发会话 - 增强版错误处理和降级机制"""
        try:
            self.current_session_id = session_id
            self.is_active = True
            
            # 🎯 错误处理：启动科大讯飞连接（带重试和降级）
            if self.xunfei_manager:
                xunfei_success = await self._start_xunfei_with_retry(session_id)
                if xunfei_success:
                    logger.info("🔀 双轨音频分发已启动")
                else:
                    logger.warning("⚠️ 科大讯飞启动失败，系统将自动降级到单轨模式（仅千问）")
                    self.xunfei_manager = None  # 禁用科大讯飞
            
            return True
            
        except Exception as e:
            logger.error(f"启动音频分发会话失败: {e}")
            return False
    
    async def _start_xunfei_with_retry(self, session_id: str, max_retries: int = 3) -> bool:
        """
        带重试机制的科大讯飞启动
        
        Args:
            session_id: 会话ID
            max_retries: 最大重试次数
            
        Returns:
            bool: 启动是否成功
        """
        for attempt in range(max_retries):
            try:
                success = await self.xunfei_manager.connect(session_id)
                if success:
                    await self.xunfei_manager.start_transcription()
                    logger.info(f"✅ 科大讯飞连接成功（第{attempt + 1}次尝试）")
                    return True
                else:
                    logger.warning(f"❌ 科大讯飞连接失败（第{attempt + 1}次尝试）")
                    
            except Exception as e:
                logger.warning(f"❌ 科大讯飞连接异常（第{attempt + 1}次尝试）: {e}")
            
            # 重试延迟
            if attempt < max_retries - 1:
                delay = 2 ** attempt  # 指数退避
                logger.info(f"⏳ {delay}秒后重试科大讯飞连接...")
                await asyncio.sleep(delay)
        
        logger.error(f"❌ 科大讯飞连接失败，已达到最大重试次数({max_retries})")
        return False
    
    async def distribute_audio(self, audio_data: bytes) -> None:
        """
        高效音频数据分发到两个ASR服务
        使用预转换和无阻塞分发优化性能
        """
        if not self.is_active:
            return
        
        try:
            # 🎯 性能优化：预转换音频格式，避免在并发任务中重复转换
            converted_audio = None
            if self.xunfei_manager and self.xunfei_manager.is_session_active:
                converted_audio = self._convert_audio_format(audio_data)
            
            # 🎯 性能优化：使用create_task创建真正的并发执行
            tasks = []
            
            # 发送到千问（现有流程）
            if self.qwen_manager:
                tasks.append(asyncio.create_task(self._send_to_qwen(audio_data)))
            
            # 发送到科大讯飞（使用预转换的音频）
            if converted_audio is not None:
                tasks.append(asyncio.create_task(self._send_to_xunfei_direct(converted_audio)))
            
            # 🎯 性能优化：使用return_exceptions=True避免一个失败影响另一个
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 记录任何异常但不抛出
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        service_name = "千问" if i == 0 else "科大讯飞"
                        logger.warning(f"⚠️ {service_name}音频发送异常: {result}")
                
        except Exception as e:
            logger.error(f"音频分发失败: {e}")
    
    async def _send_to_qwen(self, audio_data: bytes) -> None:
        """发送到千问ASR"""
        try:
            # 使用现有的千问音频发送方法
            await self.qwen_manager._send_audio_to_qwen_direct(audio_data)
        except Exception as e:
            logger.error(f"发送到千问失败: {e}")
    
    async def _send_to_xunfei(self, audio_data: bytes) -> None:
        """发送到科大讯飞ASR（兼容方法）"""
        try:
            # 音频格式转换（如果需要）
            converted_audio = self._convert_audio_format(audio_data)
            await self.xunfei_manager.send_audio_chunk(converted_audio)
        except Exception as e:
            logger.error(f"发送到科大讯飞失败: {e}")
    
    async def _send_to_xunfei_direct(self, converted_audio_data: bytes) -> None:
        """直接发送已转换的音频到科大讯飞ASR（性能优化版）"""
        try:
            await self.xunfei_manager.send_audio_chunk(converted_audio_data)
        except Exception as e:
            logger.error(f"发送到科大讯飞失败: {e}")
    
    def _convert_audio_format(self, audio_data: bytes) -> bytes:
        """
        高效音频格式转换（24kHz -> 16kHz）
        使用简单采样方法进行下采样
        """
        try:
            # 如果源和目标采样率相同，直接返回
            if settings.audio_sample_rate == settings.xunfei_sample_rate:
                return audio_data
            
            # 简单下采样：每1.5个样本取1个（24000/16000 = 1.5）
            # 这是一个快速但基本的方法，适合实时处理
            if settings.audio_sample_rate == 24000 and settings.xunfei_sample_rate == 16000:
                # PCM16格式，每个样本2字节
                samples = len(audio_data) // 2
                downsampled_samples = (samples * 2) // 3  # 16k/24k ≈ 2/3
                
                # 简单间隔采样
                downsampled_data = bytearray()
                for i in range(0, downsampled_samples * 2, 2):
                    if i < len(audio_data) - 1:
                        # 取原始索引 i * 3 // 2
                        src_idx = (i * 3) // 2
                        if src_idx < len(audio_data) - 1:
                            downsampled_data.extend(audio_data[src_idx:src_idx+2])
                
                return bytes(downsampled_data)
            
            # 其他情况直接返回原数据
            return audio_data
            
        except Exception as e:
            logger.error(f"音频格式转换失败: {e}")
            # 转换失败时返回原数据
            return audio_data
    
    async def stop_session(self) -> None:
        """停止音频分发会话"""
        try:
            self.is_active = False
            
            if self.xunfei_manager:
                await self.xunfei_manager.stop_transcription()
            
            logger.info("🛑 音频分发会话已停止")
            
        except Exception as e:
            logger.error(f"停止音频分发会话失败: {e}")
    
    async def shutdown(self) -> None:
        """关闭分发器"""
        try:
            await self.stop_session()
            
            if self.xunfei_manager:
                await self.xunfei_manager.disconnect()
            
            logger.info("🔌 音频分发器已关闭")
            
        except Exception as e:
            logger.error(f"关闭音频分发器失败: {e}") 