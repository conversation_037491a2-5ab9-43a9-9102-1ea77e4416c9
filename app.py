"""
FastAPI Application - Audio Agent Main Server

主要的FastAPI应用程序，提供WebSocket连接和HTTP端点。
整合所有Agent组件，提供统一的实时语音对话接口。

关键功能：
1. WebSocket连接管理
2. 实时音频流处理
3. Agent组件协调
4. 会话状态管理
5. 错误处理和日志记录
"""

import asyncio
import json
import base64
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.websockets import WebSocketState
from loguru import logger
import uvicorn

# Import our agent classes
from agents.orchestrator import Orchestrator
from agents.listener import Listener
from agents.tactical_thinker import TacticalThinker
from agents.strategic_thinker import StrategicThinker
from agents.decider import Decider
from agents.speaker import Speaker

# Import unified connection manager and dual-track ASR
from core.realtime_manager import RealtimeManager, set_global_manager
# 移除科大讯飞ASR管理器导入

# Global connection manager instance
realtime_manager = None
# 移除科大讯飞ASR管理器变量

# Create agent instances (Listener and Speaker will be created after connection manager)
orchestrator = Orchestrator()
listener = None  # Will be created after realtime_manager initialization
tactical_thinker = TacticalThinker()
strategic_thinker = StrategicThinker()
decider = Decider()
speaker = None  # Will be created after realtime_manager initialization

from core.config import get_realtime_config, get_doubao_config, settings, INTERVIEWER_INSTRUCTIONS, STANDARD_INSTRUCTIONS
from core.message_bus import message_bus
from core.client_factory import ClientFactory
from core.message_bus import TopicNames, create_message
from core.event_types import EventType, MessageType
from core.vad_manager import create_vad_manager, get_vad_manager, VADPreset


# Global state for active WebSocket connections
active_connections: Dict[str, WebSocket] = {}
conversation_sessions: Dict[str, Dict[str, Any]] = {}

# Connection monitoring and statistics
connection_stats = {
    'total_connections': 0,
    'active_connections': 0,
    'failed_connections': 0,
    'total_messages': 0,
    'connection_history': []
}

def log_connection_event(session_id: str, event_type: str, details: Dict[str, Any] = None):
    """记录连接事件用于监控和调试"""
    event = {
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id,
        "event_type": event_type,
        "details": details or {}
    }
    
    connection_stats['connection_history'].append(event)
    
    # 保持历史记录在合理范围内（最近100个事件）
    if len(connection_stats['connection_history']) > 100:
        connection_stats['connection_history'] = connection_stats['connection_history'][-100:]
    
    logger.info(f"📊 连接事件: {event_type} - 会话={session_id}")

def update_connection_stats(action: str):
    """更新连接统计"""
    if action == "connect":
        connection_stats['total_connections'] += 1
        connection_stats['active_connections'] += 1
    elif action == "disconnect":
        connection_stats['active_connections'] = max(0, connection_stats['active_connections'] - 1)
    elif action == "error":
        connection_stats['failed_connections'] += 1
    elif action == "message":
        connection_stats['total_messages'] += 1


async def initialize_agents():
    """初始化所有智能体"""
    global orchestrator, listener, tactical_thinker, strategic_thinker, decider, speaker, realtime_manager
    
    try:
        logger.info("🚀 开始初始化智能体系统...")
        
        logger.info("📡 初始化消息总线...")
        await message_bus.initialize()
        logger.info("✅ 消息总线初始化完成")
        
        # 获取配置
        config = get_realtime_config()
        doubao_config = get_doubao_config()
        
        logger.info("🔌 创建统一Realtime API连接管理器...")
        # 🎯 修复：正确使用global关键字创建全局单例，并传递正确的参数
        realtime_manager = RealtimeManager(
            api_key=config.get("api_key"),
            model=config.get("model", "qwen-omni-turbo-realtime"),
            base_url=config.get("base_url")
        )
        
        # 🎯 修复：设置为全局实例，供其他agents使用
        from core.realtime_manager import set_global_manager
        set_global_manager(realtime_manager)
        logger.info("✅ 全局RealtimeManager实例已设置")
        
        # 连接到阿里千问qwen-omni-turbo-realtime API
        logger.info("🌐 连接到阿里千问qwen-omni-turbo-realtime API...")
        success = await realtime_manager.connect()
        if not success:
            raise Exception("Failed to connect to 阿里千问qwen-omni-turbo-realtime API")
        logger.info("✅ 阿里千问qwen-omni-turbo-realtime API连接成功")

        # 🎯 新增：初始化VAD管理器
        vad_manager = create_vad_manager(realtime_manager)
        logger.info("✅ VAD管理器初始化完成")
            # 移除科大讯飞ASR管理器创建逻辑

        logger.info("🤖 创建音频处理代理...")
        # 🎯 修复：使用正确的类名并传递正确的参数
        listener = Listener()
        speaker = Speaker()
        
        # 为音频处理代理设置realtime_manager引用
        if hasattr(listener, 'set_realtime_manager'):
            listener.set_realtime_manager(realtime_manager)
        if hasattr(speaker, 'set_realtime_manager'):
            speaker.set_realtime_manager(realtime_manager)
        
        logger.info("✅ 音频代理创建完成")
        
        logger.info("🤖 开始初始化智能体...")
        # 🎯 修复：简化初始化逻辑，所有agents都已经创建
        agents_to_initialize = [
            ("orchestrator", orchestrator),
            ("listener", listener), 
            ("tactical_thinker", tactical_thinker),
            ("strategic_thinker", strategic_thinker),
            ("decider", decider),
            ("speaker", speaker)
        ]
        
        for agent_name, agent_instance in agents_to_initialize:
            logger.info(f"🔄 正在初始化 {agent_name}...")
            await agent_instance.initialize()
            logger.info(f"✅ {agent_name} 初始化成功")
        
        logger.info("✅ 所有智能体初始化完成")
        
        # 移除过早的订阅者检查 - 现在在WebSocket连接建立后检查
        # 注意：订阅者检查现在在websocket_conversation函数中进行
        
    except Exception as e:
        logger.error(f"❌ 智能体系统初始化失败: {e}")
        # Clean up connection manager if created
        if realtime_manager:
            await realtime_manager.disconnect()
        raise


def get_optimal_chunk_size(audio_length: int) -> int:
    """
    根据音频长度动态调整chunk size
    
    Args:
        audio_length: 音频数据长度（字节）
    
    Returns:
        int: 最优的chunk size
    """
    if audio_length < 16384:  # < 16KB
        return audio_length  # 直接发送，不分块
    elif audio_length < 65536:  # < 64KB  
        return 16384  # 16KB chunks
    elif audio_length < 131072:  # < 128KB
        return 32768  # 32KB chunks for medium audio
    else:
        return 65536  # 64KB chunks for large audio


async def check_critical_subscribers(session_id: str):
    """
    检查关键主题的订阅者状态
    
    Args:
        session_id: 会话ID，用于日志记录
    """
    try:
        # 检查关键topic的订阅者
        critical_topics = [
            TopicNames.TRANSCRIPT_DELTA,
            TopicNames.TRANSCRIPT_COMPLETED,
            TopicNames.STRATEGIC_ANALYSIS,
            TopicNames.INTERRUPTION_DECISION,
            TopicNames.AUDIO_OUTPUT,
            TopicNames.UI_UPDATE,
            TopicNames.DISRUPTIVE_ANALYSIS
        ]
        
        logger.info(f"🎯 检查会话 {session_id} 的关键topic订阅者:")
        missing_subscribers = []
        
        for topic in critical_topics:
            subscriber_count = len(message_bus.subscribers.get(topic, []))
            if subscriber_count > 0:
                logger.info(f"✅ {topic}: {subscriber_count} 个订阅者")
            else:
                logger.warning(f"⚠️ {topic}: 没有订阅者")
                missing_subscribers.append(topic)
        
        if missing_subscribers:
            logger.warning(f"❌ 会话 {session_id} 缺少关键订阅者: {missing_subscribers}")
            return False
        else:
            logger.info(f"✅ 会话 {session_id} 所有关键topic都有订阅者")
            return True
            
    except Exception as e:
        logger.error(f"检查订阅者状态时出错: {e}")
        return False


async def shutdown_agents():
    """Gracefully shutdown all agents."""
    global realtime_manager
    
    try:
        logger.info("🛑 开始关闭智能体系统...")
        
        # 🎯 修复：确保正确关闭所有agents
        agents_to_shutdown = [
            ("speaker", speaker),
            ("decider", decider), 
            ("strategic_thinker", strategic_thinker),
            ("tactical_thinker", tactical_thinker),
            ("listener", listener),
            ("orchestrator", orchestrator)
        ]
        
        for agent_name, agent in agents_to_shutdown:
            if agent:
                logger.info(f"🔄 正在关闭 {agent_name}...")
                try:
                    await agent.safe_shutdown()
                    logger.info(f"✅ {agent_name} 关闭完成")
                except Exception as e:
                    logger.error(f"❌ 关闭 {agent_name} 时出错: {e}")
        
        # 🎯 修复：正确关闭全局Realtime API连接
        if realtime_manager:
            logger.info("🔌 关闭阿里千问qwen-omni-turbo-realtime API连接...")
            try:
                await realtime_manager.disconnect()
                logger.info("✅ 阿里千问qwen-omni-turbo-realtime API连接关闭完成")
            except Exception as e:
                logger.error(f"❌ 关闭Realtime API连接时出错: {e}")
            finally:
                # 🎯 修复：清空全局引用
                from core.realtime_manager import set_global_manager
                set_global_manager(None)
                realtime_manager = None
        
        # 移除科大讯飞ASR连接关闭逻辑

        # Close all AI clients
        logger.info("🔌 关闭AI客户端连接...")
        try:
            await ClientFactory.close_all_clients()
        except Exception as e:
            logger.error(f"❌ 关闭AI客户端时出错: {e}")
        
        # Shutdown message bus last
        logger.info("📡 关闭消息总线...")
        try:
            await message_bus.shutdown()
        except Exception as e:
            logger.error(f"❌ 关闭消息总线时出错: {e}")
        
        logger.info("✅ 智能体系统关闭完成")
        
    except Exception as e:
        logger.error(f"❌ 关闭过程中发生错误: {e}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI应用生命周期管理器"""
    # 启动阶段
    logger.info("🚀 FastAPI应用正在启动...")
    await initialize_agents()
    
    # 启动所有智能体
    agents_to_start = [
        ("orchestrator", orchestrator),
        ("listener", listener),
        ("tactical_thinker", tactical_thinker),
        ("strategic_thinker", strategic_thinker),
        ("decider", decider),
        ("speaker", speaker)
    ]
    logger.info("🚀 开始启动所有智能体...")
    for agent_name, agent_instance in agents_to_start:
        if agent_instance:
            logger.info(f"▶️ 正在启动 {agent_name}...")
            try:
                await agent_instance.start()
                logger.info(f"✅ {agent_name} 启动成功")
            except Exception as e:
                logger.error(f"❌ 启动 {agent_name} 失败: {e}")
                raise
    logger.info("✅ 所有智能体启动完成")
    
    logger.info("✅ FastAPI应用启动完成")
    
    yield
    
    # 关闭阶段
    logger.info("🛑 FastAPI应用正在关闭...")
    await shutdown_agents()
    logger.info("✅ FastAPI应用关闭完成")


# Initialize FastAPI app with lifespan
app = FastAPI(
    title="Proactive AI Conversation Assistant",
    description="Real-time AI assistant with active interruption capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware for web client access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def get_system_status() -> Dict[str, Any]:
    """Get comprehensive system status."""
    try:
        # Get individual agent statuses
        agents = [orchestrator, listener, tactical_thinker, strategic_thinker, decider, speaker]
        agent_statuses = {}
        
        for agent in agents:
            agent_statuses[agent.name] = await agent.get_agent_status()
        
        # Get message bus status
        message_bus_status = await message_bus.get_status()
        
        # Get connection statistics
        return {
            "system_health": "healthy",  # Will be calculated based on agent states
            "timestamp": datetime.now().isoformat(),
            "agents": agent_statuses,
            "message_bus": message_bus_status,
            "connection_stats": connection_stats,
            "active_sessions": len(conversation_sessions),
            "websocket_connections": len(active_connections)
        }
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return {
            "system_health": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/health")
async def health_check():
    """Enhanced health check with agent status and event handler verification."""
    try:
        system_status = await get_system_status()
        
        # Determine overall health
        health_status = "healthy"
        agent_issues = []
        
        for agent_name, agent_status in system_status.get("agents", {}).items():
            if not agent_status.get("is_running", False):
                health_status = "degraded"
                agent_issues.append(f"{agent_name} not running")
            elif agent_status.get("state") == "error":
                health_status = "unhealthy"
                agent_issues.append(f"{agent_name} in error state")
        
        # ✅ 增强：检查关键事件处理器注册状态
        event_handler_issues = []
        if realtime_manager:
            handler_counts = realtime_manager.get_status().get("event_handlers_count", {})
            critical_events = [
                "conversation.item.input_audio_transcription.delta",
                "conversation.item.input_audio_transcription.completed", 
                "response.audio.delta",
                "response.audio.done"
            ]
            
            for event in critical_events:
                count = handler_counts.get(event, 0)
                if count == 0:
                    event_handler_issues.append(f"No handler for {event}")
                    if health_status == "healthy":
                        health_status = "degraded"
        
        # ✅ 增强：检查Realtime连接状态
        realtime_issues = []
        if realtime_manager:
            realtime_status = realtime_manager.get_status()
            if not realtime_status.get("is_connected", False):
                realtime_issues.append("Realtime API not connected")
                health_status = "unhealthy"
            if not realtime_status.get("is_session_active", False):
                realtime_issues.append("Realtime session not active")
                if health_status == "healthy":
                    health_status = "degraded"
        else:
            realtime_issues.append("Realtime manager not initialized")
            health_status = "unhealthy"
        
        response = {
            "status": health_status,
            "timestamp": datetime.now().isoformat(),
            "system": system_status,
            "issues": {
                "agents": agent_issues,
                "event_handlers": event_handler_issues,
                "realtime_connection": realtime_issues
            },
            "diagnostics": {
                "realtime_status": realtime_manager.get_status() if realtime_manager else None,
                "active_sessions": len(conversation_sessions),
                "websocket_connections": len(active_connections)
            }
        }
        
        # Return appropriate HTTP status
        if health_status == "healthy":
            return JSONResponse(content=response, status_code=200)
        elif health_status == "degraded":
            return JSONResponse(content=response, status_code=206)  # Partial Content
        else:
            return JSONResponse(content=response, status_code=503)  # Service Unavailable
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            },
            status_code=500
        )


@app.get("/status")
async def get_status():
    """Get detailed system status."""
    try:
        status = await get_system_status()
        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return JSONResponse(
            content={"error": str(e)}, 
            status_code=500
        )


@app.get("/")
async def root():
    """Root endpoint with system information."""
    return {
        "name": "Proactive AI Conversation Assistant",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "status": "/status", 
            "websocket": "/ws/conversation"
        },
        "timestamp": datetime.now().isoformat()
    }


@app.websocket("/ws/conversation")
async def websocket_conversation(websocket: WebSocket):
    """WebSocket endpoint for conversation with speech interruption support."""
    client_host = websocket.client.host
    client_port = websocket.client.port
    session_id = str(uuid.uuid4())
    
    await websocket.accept()
    logger.info(f"🎯 WebSocket连接已建立: 会话={session_id}, 客户端={client_host}:{client_port}")
    
    # Session initialization and connection management
    conversation_sessions[session_id] = {
        "start_time": datetime.now(),
        "client_host": client_host,
        "client_port": client_port,
        "message_count": 0,
        "last_heartbeat": datetime.now(),
        "is_active": True,
        "direct_audio_enabled": True,  # 默认启用直接音频通道
        "websocket": websocket
    }
    
    # 🎯 修复：先启动RealtimeManager会话，再创建Orchestrator会话，确保VAD配置成功
    if realtime_manager:
        # 设置当前会话ID到realtime_manager
        realtime_manager.set_current_session_id(session_id)
        
        # 🎯 关键修复：先启动realtime_manager会话，确保VAD配置有效会话
        session_config = {
            "modalities": ["text", "audio"],
            "voice": "alloy",  # 默认音色
            "input_audio_format": "pcm16",
            "output_audio_format": "pcm16"
        }
        
        realtime_success = await realtime_manager.start_session(session_config)
        if not realtime_success:
            logger.error(f"❌ 无法启动RealtimeManager会话: {session_id}")
            await websocket.close(code=1011, reason="Failed to start realtime session")
            return
        logger.info(f"✅ RealtimeManager会话启动成功: {session_id}")

        # 🎯 核心修复：会话启动后，立即设置默认的系统指令（AI身份）
        try:
            await realtime_manager.update_session_config({
                "instructions": STANDARD_INSTRUCTIONS
            })
            logger.info(f"✅ Set initial 'Audio Manus' persona for session {session_id}")
        except Exception as e:
            logger.error(f"Failed to set initial persona: {e}")
        
        # 移除科大讯飞ASR会话启动逻辑
        
    # 注册WebSocket连接用于直接音频通道
    if realtime_manager:
        success = await realtime_manager.register_websocket_connection(session_id, websocket)
        if success:
            logger.info(f"✅ 已注册直接音频通道: 会话={session_id}")
        else:
            logger.warning(f"⚠️ 直接音频通道注册失败: 会话={session_id}")
    else:
        logger.error("❌ realtime_manager未初始化，无法注册直接音频通道")
        await websocket.close(code=1011, reason="RealtimeManager not initialized")
        return

    # 🎯 核心改造 1: 定义一个与当前WebSocket绑定的直接发送函数
    async def direct_send_to_client(message_body: Dict[str, Any]):
        """A direct channel to send critical commands to this specific client."""
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_text(json.dumps(message_body))
                logger.info(f"✅ Directly sent message to client {session_id}: {message_body.get('type')}")
            except Exception as e:
                logger.error(f"💥 Failed to directly send message to client {session_id}: {e}", exc_info=True)
        else:
            logger.warning(f"⚠️ Attempted to send to disconnected client {session_id}, message dropped.")

    # 🎯 核心改造 2: 创建Orchestrator会话时，将此函数传递进去
    try:
        active_session_id = await orchestrator.create_session(
            session_id=session_id, 
            send_callback=direct_send_to_client
        )
        if not active_session_id:
            logger.error("❌ Orchestrator未能创建会话")
            await websocket.close(code=1011, reason="Orchestrator failed to create session")
            return
        logger.info(f"✅ Orchestrator session created successfully: {active_session_id}")
    except Exception as e:
        logger.error(f"❌ Failed to create orchestrator session: {e}", exc_info=True)
        await websocket.close(code=1011, reason="Session creation failed")
        return
    
    # Update global stats
    update_connection_stats("connect")
    
    try:
        # Subscribe to transcript topics
        async def transcript_callback(topic: str, message_data: Dict[str, Any]):
            """Forward transcript messages to WebSocket client"""
            try:
                # WebSocket状态检查防护
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket not connected (state: {websocket.client_state.name}), skipping transcript_callback for session {session_id}")
                    return

                # Check session_id in both data and top-level
                message_session_id = message_data.get("session_id") or message_data.get("data", {}).get("session_id")

                # If no session_id, or if it matches this connection's session
                if not message_session_id or message_session_id == session_id:
                    data = message_data.get("data", {})
                    text = data.get("text", "")
                    
                    if text and text.strip():
                        # 🎯 修复：根据消息类型正确设置transcript类型
                        is_final = data.get("is_final", False)
                        is_delta = data.get("is_delta", False)

                        # 确定正确的消息类型
                        if is_delta and not is_final:
                            message_type = "transcript_delta"
                        elif is_final:
                            message_type = "transcript_complete"
                        else:
                            # 默认情况，根据topic判断
                            if topic == TopicNames.TRANSCRIPT_DELTA:
                                message_type = "transcript_delta"
                            elif topic == TopicNames.TRANSCRIPT_COMPLETED:
                                message_type = "transcript_complete"
                            else:
                                message_type = "transcript_complete"  # 默认为完成

                        transcript_message = {
                            "type": message_type,
                            "text": text,
                            "content": text,  # 添加content字段以兼容前端
                            "session_id": session_id,
                            "is_final": is_final,
                            "confidence": data.get("confidence", 0.0),
                            "timestamp": data.get("timestamp", datetime.now().isoformat()),
                            "source": data.get("source", "realtime_api")
                        }
                        await websocket.send_text(json.dumps(transcript_message))
                        logger.debug(f"Transcript ({message_type}) forwarded to client: {text[:50]}...")
            except Exception as e:
                # 只在连接有效时记录错误，避免污染日志
                if websocket.client_state == WebSocketState.CONNECTED:
                    logger.error(f"Error forwarding transcript: {e}")
        
        # Subscribe to AI response events
        async def ai_response_callback(topic: str, message_data: Dict[str, Any]):
            """Forward AI response messages to WebSocket client"""
            try:
                data = message_data.get("data", {})
                # 🎯 修复：支持多种AI响应文本字段
                response_text = (
                    data.get("text", "") or
                    data.get("content", "") or
                    data.get("transcript", "") or
                    data.get("response_text", "")
                )
                message_session_id = data.get("session_id")

                # 确保消息属于当前会话
                if message_session_id and message_session_id != session_id:
                    return

                # WebSocket状态检查防护
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket not connected (state: {websocket.client_state.name}), skipping ai_response_callback for session {session_id}")
                    return
                
                if response_text and response_text.strip():
                    response_id = data.get("response_id", f"resp_{int(datetime.now().timestamp() * 1000)}")
                    timestamp = data.get("timestamp")
                    
                    # 使用response_id和text的哈希创建唯一的message_key（用于前端去重）
                    import hashlib
                    message_key = hashlib.md5(f"{response_id}_{response_text}".encode()).hexdigest()
                    
                    # 🎯 核心修复：正确设置streaming和complete标志
                    is_delta = data.get("is_delta", False)
                    is_final = data.get("is_final", False)
                    
                    await websocket.send_text(json.dumps({
                        "type": "ai_response",
                        "text": response_text,
                        "content": response_text,
                        "session_id": session_id,
                        "response_id": response_id,
                        "timestamp": timestamp or datetime.now().isoformat(),
                        "message_key": message_key,  # 用于前端去重
                        "streaming": is_delta,  # 增量消息标记为流式
                        "complete": is_final    # 最终消息标记为完成
                    }))
                    logger.debug(f"AI response forwarded to client: {response_text[:50]}...")
            except Exception as e:
                # 只在连接有效时记录错误，避免污染日志
                if websocket.client_state == WebSocketState.CONNECTED:
                    logger.error(f"Error forwarding AI response: {e}")
        
        # Subscribe to transcript topics
        await message_bus.subscribe(TopicNames.RESPONSE_EVENTS, ai_response_callback)
        
        # Subscribe to audio output events - 修复版本，正确处理直接通道降级
        async def audio_output_callback(topic: str, message_data: Dict[str, Any]):
            """Forward audio output to WebSocket client - 智能降级模式"""
            try:
                data = message_data.get("data", {})
                audio_source = data.get("source", "unknown")
                direct_channel_failed = data.get("direct_channel_failed", False)
                
                # 🎯 修复：检查音频来源，只跳过非降级的消息总线消息
                session_info = conversation_sessions.get(session_id, {})
                if session_info.get("direct_audio_enabled", False):
                    # 如果是直接通道降级的消息，允许处理
                    if audio_source == "direct_fallback" or direct_channel_failed:
                        logger.info(f"🎵 处理直接通道降级音频消息: session={session_id}")
                    else:
                        # 如果是普通的消息总线消息且直接通道启用，跳过（避免重复）
                        logger.debug(f"🎵 跳过消息总线音频消息 - 直接通道已启用: session={session_id}")
                        return
                
                # WebSocket状态检查防护
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket not connected (state: {websocket.client_state.name}), skipping audio_output_callback for session {session_id}")
                    return
                
                audio_data = data.get("audio_data", "")
                
                # 🔍 添加详细的音频数据验证日志
                logger.info(f"🔊 音频输出回调被触发 (消息总线模式):")
                logger.info(f"   🎵 会话ID: {session_id}")
                if audio_data:
                    logger.info(f"   📏 音频数据长度: {len(audio_data)} 字符 (base64)")
                    logger.info(f"   🎼 音频格式: {data.get('format', '未指定')}")
                    logger.info(f"   📻 采样率: {data.get('sample_rate', '未指定')}")
                    logger.info(f"   ⏱️ 时间戳: {data.get('timestamp', '未指定')}")
                    logger.info(f"   🏷️ 数据来源: {data.get('source', '未指定')}")
                
                # AI音频输出是全局的，不需要session_id过滤
                if audio_data:
                    # 使用动态分块大小
                    chunk_size = get_optimal_chunk_size(len(audio_data))
                    if len(audio_data) > chunk_size:
                        # 分多个消息发送大音频块
                        for i in range(0, len(audio_data), chunk_size):
                            chunk = audio_data[i:i + chunk_size]
                            chunk_message = {
                                "type": "audio_output_chunk",
                                "data": chunk,
                                "chunk_index": i // chunk_size,
                                "total_chunks": (len(audio_data) + chunk_size - 1) // chunk_size,
                                "format": data.get("format", "pcm16"),
                                "sample_rate": data.get("sample_rate", 24000),
                                "session_id": session_id,
                                "timestamp": datetime.now().isoformat(),
                                "source": "message_bus_fallback"
                            }
                            await websocket.send_text(json.dumps(chunk_message))
                            # 小延迟让出控制权，避免网络拥塞
                            await asyncio.sleep(0.001)
                    else:
                        # 小音频数据直接发送
                        await websocket.send_text(json.dumps({
                            "type": "audio_output",
                            "data": audio_data,
                            "format": data.get("format", "pcm16"),
                            "sample_rate": data.get("sample_rate", 24000),
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat(),
                            "source": "message_bus_fallback"
                        }))
                    logger.debug(f"Audio output forwarded to client (fallback mode), length: {len(audio_data)}")
            except Exception as e:
                # 只在连接有效时记录错误，避免污染日志
                if websocket.client_state == WebSocketState.CONNECTED:
                    logger.error(f"Error forwarding audio output: {e}")
                    # 如果是连接错误，标记会话需要清理
                    if "websocket" in str(e).lower() or "connection" in str(e).lower():
                        logger.warning(f"WebSocket connection issue detected for session {session_id}")
                        # 可以在这里触发会话清理或重连逻辑

        await message_bus.subscribe(TopicNames.TRANSCRIPT_DELTA, transcript_callback)
        await message_bus.subscribe(TopicNames.TRANSCRIPT_COMPLETED, transcript_callback)
        
        await message_bus.subscribe(TopicNames.AUDIO_OUTPUT, audio_output_callback)
        
        # --- Start of Fix ---
        # Subscribe to audio playback control events
        async def audio_playback_callback(topic: str, message_data: Dict[str, Any]):
            """Forward audio playback control commands to WebSocket client"""
            try:
                # WebSocket状态检查防护
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket not connected (state: {websocket.client_state.name}), skipping audio_playback_callback for session {session_id}")
                    return

                data = message_data.get("data", {})
                command = data.get("command", "")
                
                if command == "stop":
                    # Send audio playback stop command to frontend
                    await websocket.send_text(json.dumps({
                        "type": "audio_playback_stop",
                        "command": "stop",
                        "reason": data.get("reason", "unknown"),
                        "timestamp": data.get("timestamp", datetime.now().isoformat()),
                        "session_id": session_id
                    }))
                    logger.info(f"✅ Sent audio_playback_stop command to frontend: session={session_id}")
                    
            except Exception as e:
                # 只在连接有效时记录错误，避免污染日志
                if websocket.client_state == WebSocketState.CONNECTED:
                    logger.error(f"Error forwarding audio playback control: {e}")
        
        await message_bus.subscribe(TopicNames.AUDIO_PLAYBACK, audio_playback_callback)
        # --- End of Fix ---

        # --- Start of Speech Events Fix ---
        # Subscribe to speech events and forward them to WebSocket client
        async def speech_events_callback(topic: str, message_data: Dict[str, Any]):
            """Forward speech events to WebSocket client"""
            try:
                # WebSocket状态检查防护
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket not connected (state: {websocket.client_state.name}), skipping speech_events_callback for session {session_id}")
                    return

                # Handle different message formats from different topics
                if topic == TopicNames.SPEECH_EVENTS:
                    # Format from RealtimeManager via SPEECH_EVENTS topic
                    event_type = message_data.get("event_type")
                    data = message_data.get("data", {})
                    timestamp = data.get("timestamp", datetime.now().timestamp())

                    if event_type == "speech_started":
                        message_type = "speech_started"
                    elif event_type == "speech_stopped":
                        message_type = "speech_stopped"
                    else:
                        return  # Unknown event type

                elif topic in [TopicNames.SPEECH_STARTED, TopicNames.SPEECH_STOPPED]:
                    # Format from Listener via legacy speech topics
                    data = message_data
                    timestamp = data.get("timestamp", datetime.now().isoformat())
                    message_type = "speech_started" if topic == TopicNames.SPEECH_STARTED else "speech_stopped"
                else:
                    return  # Unknown topic

                # Send event to frontend
                await websocket.send_text(json.dumps({
                    "type": message_type,
                    "timestamp": timestamp if isinstance(timestamp, str) else datetime.fromtimestamp(timestamp).isoformat(),
                    "session_id": session_id,
                    "source": "vad_detection"
                }))
                logger.info(f"✅ Sent {message_type} event to frontend: session={session_id}")

            except Exception as e:
                # 只在连接有效时记录错误，避免污染日志
                if websocket.client_state == WebSocketState.CONNECTED:
                    logger.error(f"Error forwarding speech events: {e}")

        # Subscribe to both SPEECH_EVENTS (from RealtimeManager) and legacy speech topics (from Listener)
        await message_bus.subscribe(TopicNames.SPEECH_EVENTS, speech_events_callback)
        await message_bus.subscribe(TopicNames.SPEECH_STARTED, speech_events_callback)
        await message_bus.subscribe(TopicNames.SPEECH_STOPPED, speech_events_callback)
        # --- End of Speech Events Fix ---
        
        # 🎯 修复：正确的UI更新订阅，处理合作型附和
        async def ui_update_callback(topic: str, message_data: Dict[str, Any]):
            """转发合作型附和（文本/表情符号）到WebSocket客户端"""
            # 🎯 调试日志 3: 确认回调函数被触发
            logger.info(f"🔍 APP.PY: ui_update_callback triggered for topic '{topic}'. Message data: {message_data}")
            
            try:
                # 🎯 调试日志 4: 检查WebSocket状态
                logger.info(f"🔍 APP.PY: WebSocket state check - Current state: {websocket.client_state.name} for session {session_id}")
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"⚠️ APP.PY: WebSocket not connected (state: {websocket.client_state.name}), skipping UI update for session {session_id}")
                    return

                data = message_data.get("data", {})
                message_session_id = data.get("session_id")

                # 🎯 修复：改进会话ID匹配逻辑
                # 如果没有指定session_id，则向所有连接广播（用于全局UI更新）
                # 如果指定了session_id，则只发送给匹配的连接
                if message_session_id and message_session_id != session_id:
                    return
                
                # 🎯 确保这里的逻辑不会生成 `type: 'control_interruption'`
                message_type = data.get("type", "cooperative_interjection")
                
                # 🎯 修复：移除可能与专属控制通道冲突的打断消息处理
                # 现在所有的打断控制都由Orchestrator的直接通道处理，这里只处理UI显示类消息
                if message_type == "cooperative_interjection":
                    # 处理合作型附和消息（这是安全的，前端可以有另一个处理器来显示它）
                    ui_message = {
                        "type": "cooperative_interjection",
                        "content": data.get("text", data.get("content", "")),
                        "text": data.get("text", data.get("content", "")),
                        "session_id": session_id,
                        "timestamp": datetime.now().isoformat(),
                        "source": "tactical_thinker"
                    }
                else:
                    # 其他类型的UI更新（但绝不包括control_interruption）
                    ui_message = {
                        "type": message_type,  # 保持原始类型，但确保不是控制类型
                        "content": data.get("text", data.get("content", "")),
                        "session_id": session_id,
                        "timestamp": datetime.now().isoformat(),
                        "source": data.get("source", "unknown")
                    }
                
                # 🎯 调试日志 5: 确认准备发送到前端
                logger.info(f"🔍 APP.PY: Preparing to send message to client {session_id}. Message: {ui_message}")
                
                # 发送UI更新消息
                await websocket.send_text(json.dumps(ui_message))
                
                # 🎯 调试日志 6: 确认成功发送
                logger.info(f"✅ APP.PY: Successfully sent UI update to client {session_id}.")
                
                # 根据消息类型显示不同的日志
                if message_type == "cooperative_interjection":
                    logger.info(f"🤝 已发送合作型附和到客户端: '{ui_message.get('text', '')}'")
                else:
                    logger.info(f"📝 已发送UI更新到客户端: 类型={message_type}, 内容='{ui_message.get('content', '')}''")

            except Exception as e:
                # 🎯 调试日志 7: 捕获发送时的具体异常
                logger.error(f"💥 APP.PY: Exception in ui_update_callback for session {session_id}: {e}", exc_info=True)
                # 记录WebSocket状态以帮助诊断
                logger.error(f"💥 APP.PY: WebSocket state during error: {websocket.client_state.name}")

        await message_bus.subscribe(TopicNames.UI_UPDATE, ui_update_callback)
        
        # 🎯 在所有订阅建立后检查订阅者状态
        logger.info(f"✅ 已为会话 {session_id} 设置所有消息转发订阅")
        
        # 检查关键订阅者状态（在适当时机）
        await asyncio.sleep(0.5)  # 短暂延迟确保所有订阅生效
        subscriber_check_result = await check_critical_subscribers(session_id)
        if subscriber_check_result:
            logger.info(f"✅ 会话 {session_id} 订阅者检查通过")
        else:
            logger.warning(f"⚠️ 会话 {session_id} 某些关键订阅者缺失，但继续运行")

        # Main WebSocket message loop
        while True:
            try:
                # Receive message from client
                message = await websocket.receive_text()
                data = json.loads(message)
                
                logger.debug(f"📥 收到消息: 会话={session_id}, 类型={data.get('type', 'unknown')}, 大小={len(message)}字节")
                
                # 更新消息统计
                update_connection_stats("message")
                if session_id in conversation_sessions:
                    conversation_sessions[session_id]["message_count"] += 1
                
                await handle_websocket_message(session_id, data, websocket)
                
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket连接断开: 会话={session_id}, 客户端={client_host}:{client_port}")
                break
            except json.JSONDecodeError as e:
                logger.warning(f"💥 JSON解析错误: 会话={session_id}, 错误={e}, 原始消息={message[:100]}...")
                # 🎯 增强：检查WebSocket状态后再发送错误消息
                try:
                    if websocket.client_state.name == 'CONNECTED':
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": "Invalid JSON format"
                        }))
                except Exception as send_error:
                    logger.error(f"❌ 无法发送JSON错误消息到客户端: {send_error}")
            except Exception as e:
                logger.error(f"💥 WebSocket处理错误: 会话={session_id}, 错误类型={type(e).__name__}, 错误={e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
                
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        
    finally:
        # 更新断开连接统计
        update_connection_stats("disconnect")
        
        # 🎯 修复：正确注销直接音频连接
        if realtime_manager:
            try:
                # 从全局realtime_manager中注销WebSocket连接
                success = await realtime_manager.unregister_websocket_connection(session_id)
                if success:
                    logger.info(f"✅ 直接音频连接已注销: session={session_id}")
                else:
                    logger.warning(f"⚠️ 直接音频连接注销失败: session={session_id}")
                
                # 清除当前会话ID（如果是当前会话）
                if hasattr(realtime_manager, 'current_session_id') and realtime_manager.current_session_id == session_id:
                    realtime_manager.clear_current_session_id()
                    
            except Exception as e:
                logger.error(f"❌ 注销直接音频连接失败: session={session_id}, error={e}")
        
        # 移除科大讯飞ASR会话断开逻辑
        

        # Unsubscribe from transcript events
        try:
            await message_bus.unsubscribe(TopicNames.TRANSCRIPT_DELTA, transcript_callback)
            await message_bus.unsubscribe(TopicNames.TRANSCRIPT_COMPLETED, transcript_callback)
            await message_bus.unsubscribe(TopicNames.RESPONSE_EVENTS, ai_response_callback)
            await message_bus.unsubscribe(TopicNames.AUDIO_OUTPUT, audio_output_callback) # Unsubscribe audio output
            await message_bus.unsubscribe(TopicNames.AUDIO_PLAYBACK, audio_playback_callback) # Unsubscribe audio playback control
            await message_bus.unsubscribe(TopicNames.UI_UPDATE, ui_update_callback) # Unsubscribe UI updates
            logger.info(f"✅ 已取消会话 {session_id} 的转录消息订阅")
        except Exception as e:
            logger.error(f"Error unsubscribing transcript events: {e}")
        
        # 记录会话结束事件
        session_info = conversation_sessions.get(session_id, {})
        end_time = datetime.now()
        start_time = session_info.get("start_time", end_time)
        duration = (end_time - start_time).total_seconds()
        
        log_connection_event(session_id, "disconnected", {
            "duration_seconds": duration,
            "message_count": session_info.get("message_count", 0),
            "client_host": client_host,
            "client_port": client_port,
            "direct_audio_used": session_info.get("direct_audio_enabled", False)
        })
        
        # 🎯 修复：安全清理speaker音频输出回调
        if speaker:
            try:
                if hasattr(speaker, 'clear_audio_output_callback'):
                    speaker.clear_audio_output_callback()
                    logger.debug(f"✅ Cleared speaker audio callback for session {session_id}")
                else:
                    logger.debug(f"ℹ️ Speaker does not have clear_audio_output_callback method")
            except Exception as e:
                logger.error(f"Error clearing speaker callback: {e}")
        else:
            logger.debug(f"ℹ️ Speaker not initialized, skipping callback cleanup")
        
        # Cleanup session
        if session_id in active_connections:
            del active_connections[session_id]
        if session_id in conversation_sessions:
            del conversation_sessions[session_id]
            
        # 🎯 增强：安全地通知orchestrator会话结束
        try:
            await orchestrator.end_session(session_id)
            logger.info(f"✅ Orchestrator会话已结束: {session_id}")
        except Exception as e:
            logger.error(f"❌ 结束Orchestrator会话失败: {session_id}, error={e}")
        
        logger.info(f"🧹 会话清理完成: {session_id}, 持续时间: {duration:.1f}秒")


async def handle_websocket_message(session_id: str, data: Dict[str, Any], websocket: WebSocket):
    """Handle incoming WebSocket messages from client."""
    try:
        message_type = data.get("type")
        logger.debug(f"📥 处理WebSocket消息: 会话={session_id}, 类型={message_type}")
        
        if message_type == "audio_input":
            # Audio data from client microphone
            if not listener.is_running:
                logger.warning(f"⚠️ Listener未运行，忽略音频输入")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Audio system not ready"
                }))
                return

            audio_data = base64.b64decode(data.get("data", ""))
            logger.info(f"🎤 收到音频块: {len(audio_data)} bytes, session={session_id}")
            
            # 简化为单轨音频处理：发送到千问轨道（通过Listener）
            if listener:
                await listener.process_audio_chunk(audio_data)
            else:
                logger.warning(f"⚠️ Listener不可用，无法处理音频: session={session_id}")
            
            
        elif message_type == "start_conversation":
            # Start new conversation with mode selection
            logger.info(f"🎯 请求启动新会话: {session_id}")
            
            # 检查智能体状态
            if not orchestrator.is_running:
                logger.error(f"❌ Orchestrator未运行")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "System not ready - orchestrator offline"
                }))
                return
                
            if not listener.is_running:
                logger.error(f"❌ Listener未运行")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "System not ready - listener offline"
                }))
                return
            
            # 发送模式选择请求
            await websocket.send_text(json.dumps({
                "type": "mode_selection_required",
                "message": "请选择对话模式",
                "options": {
                    "standard": "标准助手模式 - 友好的语音助手",
                    "interviewer": "面试官模式 - AI技术面试官"
                },
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            }))
            logger.info(f"📋 已发送模式选择请求: {session_id}")
            
        elif message_type == "mode_selection":
            # Handle mode selection and update session configuration
            logger.info(f"🎯 处理模式选择: {session_id}")
            
            selected_mode = data.get("mode", "standard")
            logger.info(f"📋 用户选择模式: {selected_mode}")
            
            try:
                # Configure interviewer mode if selected
                if selected_mode == "interviewer":
                    logger.info("🎯 配置面试官模式...")
                    success = await decider.configure_interviewer_mode("standard")
                    
                    if success and realtime_manager:
                        session_update_success = await realtime_manager.update_session_config({
                            "instructions": INTERVIEWER_INSTRUCTIONS
                        })
                        if session_update_success:
                            logger.info("✅ 面试官模式配置成功")
                        else:
                            logger.warning("⚠️ 面试官指令更新失败")
                else:
                    logger.info("🤖 使用标准助手模式")
                    # 🎯 修复：为标准模式也更新session配置
                    if realtime_manager:
                        await realtime_manager.update_session_config({
                            "instructions": STANDARD_INSTRUCTIONS
                        })
                
                # 🎯 修复：会话已在连接时创建，这里只发送配置完成确认
                logger.info(f"🎯 会话配置完成，发送启动确认: {session_id}")
                
                # Send success response (session already active)
                await websocket.send_text(json.dumps({
                    "type": "conversation_started",
                    "session_id": session_id,
                    "mode": selected_mode,
                    "timestamp": datetime.now().isoformat()
                }))
                logger.info(f"🎉 会话配置完成: {session_id} (模式: {selected_mode})")
                    
            except Exception as e:
                logger.error(f"💥 会话配置异常: {session_id}, 错误={e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to configure conversation: {str(e)}"
                }))
            
        elif message_type == "end_conversation":
            # End conversation
            logger.info(f"🛑 结束会话: {session_id}")
            
            try:
                # Stop listener session first
                await listener.stop_session()
                logger.info(f"✅ Listener会话已停止")
                
                # End orchestrator session
                await orchestrator.end_session(session_id)
                logger.info(f"✅ Orchestrator会话已结束")
                
                await websocket.send_text(json.dumps({
                    "type": "conversation_ended",
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat()
                }))
                logger.info(f"🎉 会话结束成功: {session_id}")
                
            except Exception as e:
                logger.error(f"💥 会话结束异常: {session_id}, 错误={e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Error ending conversation: {str(e)}"
                }))
            
        elif message_type == "configure_interruption":
            # Configure interruption settings
            logger.info(f"⚙️ 配置打断设置: {session_id}")
            
            try:
                cooperative_threshold = data.get("cooperative_threshold")
                disruptive_threshold = data.get("disruptive_threshold")
                
                if cooperative_threshold is not None:
                    decider.cooperative_threshold = cooperative_threshold
                if disruptive_threshold is not None:
                    decider.disruptive_threshold = disruptive_threshold
                    
                await websocket.send_text(json.dumps({
                    "type": "configuration_updated",
                    "thresholds": {
                        "cooperative": decider.cooperative_threshold,
                        "disruptive": decider.disruptive_threshold
                    }
                }))
                logger.info(f"✅ 打断设置配置成功")
                
            except Exception as e:
                logger.error(f"💥 配置打断设置失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to configure interruption: {str(e)}"
                }))
                
        elif message_type == "configure_interviewer_mode":
            # Configure AI interviewer mode
            logger.info(f"⚙️ 配置AI面试官模式: {session_id}")
            
            try:
                mode = data.get("mode", "disabled")
                config_params = {
                    "max_speaking_time": data.get("max_speaking_time", 10),
                    "force_interrupt_after": data.get("force_interrupt_after", 15),
                    "cooperative_threshold": data.get("cooperative_threshold", 0.7),
                    "disruptive_threshold": data.get("disruptive_threshold", 0.5)
                }
                
                # 配置Decider的面试官模式
                success = await decider.configure_interviewer_mode(mode, **config_params)
                
                # 动态更新Realtime API的AI身份设置
                if success and realtime_manager:
                    try:
                        # 根据模式选择对应的AI指令
                        if mode != "disabled":
                            ai_instructions = INTERVIEWER_INSTRUCTIONS
                            logger.info("🎯 切换到面试官AI身份")
                        else:
                            ai_instructions = STANDARD_INSTRUCTIONS 
                            logger.info("🤖 切换到标准助手AI身份")
                        
                        # 动态更新session配置
                        session_update_success = await realtime_manager.update_session_config({
                            "instructions": ai_instructions
                        })
                        
                        if not session_update_success:
                            logger.warning("⚠️ AI身份更新失败，但面试官模式配置成功")
                        else:
                            logger.info("✅ AI身份动态更新成功")
                            
                    except Exception as session_e:
                        logger.error(f"💥 更新AI身份失败: {session_e}")
                        # 不影响整体配置，继续执行
                
                if success:
                    interviewer_status = decider.get_interviewer_status()
                    await websocket.send_text(json.dumps({
                        "type": "interviewer_mode_configured",
                        "mode": mode,
                        "status": interviewer_status,
                        "message": f"AI面试官模式已设置为: {mode}"
                    }))
                    logger.info(f"✅ AI面试官模式配置成功: {mode}")
                else:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"Failed to configure interviewer mode: {mode}"
                    }))
                    logger.error(f"❌ AI面试官模式配置失败: {mode}")
                    
            except Exception as e:
                logger.error(f"💥 配置AI面试官模式失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to configure interviewer mode: {str(e)}"
                }))
                
        elif message_type == "get_interviewer_status":
            # Get current interviewer mode status
            try:
                interviewer_status = decider.get_interviewer_status()
                await websocket.send_text(json.dumps({
                    "type": "interviewer_status",
                    "status": interviewer_status
                }))
                logger.debug(f"📊 发送面试官状态: {interviewer_status['interviewer_mode']}")
                
            except Exception as e:
                logger.error(f"💥 获取面试官状态失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to get interviewer status: {str(e)}"
                }))
                
        elif message_type == "force_interrupt":
            # Manual interrupt trigger (for testing)
            logger.info(f"🛑 手动触发打断: {session_id}")
            
            try:
                interrupt_type = data.get("interrupt_type", "manual")
                reason = data.get("reason", "Manual test interrupt")
                suggested_response = data.get("suggested_response", "Let me interrupt for a moment.")
                
                # 创建手动打断决策
                manual_decision = {
                    "type": interrupt_type,
                    "timing": "immediate",
                    "reason": reason,
                    "confidence": 1.0,
                    "manual_trigger": True,
                    "suggested_response": suggested_response
                }
                
                # 发送打断决策到Orchestrator
                interruption_msg = create_message(
                    event_type=EventType.INTERRUPTION_DECISION,
                    message_type=MessageType.INTERRUPTION_SIGNAL,
                    data={
                        "agent_id": "manual_trigger",
                        "decision": manual_decision,
                        "transcript": "",
                        "timestamp": datetime.now().isoformat()
                    }
                )
                
                await message_bus.publish(TopicNames.INTERRUPTION_DECISION, interruption_msg)
                
                await websocket.send_text(json.dumps({
                    "type": "interrupt_triggered",
                    "decision": manual_decision,
                    "message": "Manual interrupt triggered successfully"
                }))
                logger.info(f"✅ 手动打断触发成功: {interrupt_type}")
                
            except Exception as e:
                logger.error(f"💥 手动打断触发失败: {e}", exc_info=True)
                await websocket.send_text(json.dumps({
                    "type": "error", 
                    "message": f"Failed to trigger interrupt: {str(e)}"
                }))
        
        elif message_type == "manual_speech_end":
            # Handle manual speech end from frontend
            logger.info(f"🎯 收到手动语音结束信号: {session_id}")
            logger.info(f"🎯 DEBUG: 完整消息内容: {data}")

            try:
                # 检查listener和realtime_manager状态
                logger.info(f"🎯 DEBUG: listener存在: {listener is not None}")
                logger.info(f"🎯 DEBUG: listener.realtime_manager存在: {hasattr(listener, 'realtime_manager') and listener.realtime_manager is not None}")

                # 通知RealtimeManager强制结束当前语音检测
                if listener and hasattr(listener, 'realtime_manager') and listener.realtime_manager:
                    logger.info(f"🎯 DEBUG: 调用realtime_manager.force_speech_end()")
                    await listener.realtime_manager.force_speech_end()
                    logger.info(f"✅ 已通知RealtimeManager强制结束语音检测")
                else:
                    logger.error(f"❌ 无法访问RealtimeManager: listener={listener}, realtime_manager={getattr(listener, 'realtime_manager', None)}")

                # 发送确认消息
                await websocket.send_text(json.dumps({
                    "type": "manual_speech_end_ack",
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat()
                }))
                logger.info(f"✅ 发送manual_speech_end_ack确认消息")

            except Exception as e:
                logger.error(f"💥 处理手动语音结束失败: {e}", exc_info=True)
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to handle manual speech end: {str(e)}"
                }))

        elif message_type == "ping":
            # Handle ping message for heartbeat
            logger.debug(f"🏓 收到ping消息: {session_id}")

            try:
                timestamp = data.get("timestamp", datetime.now().isoformat())

                # 响应pong消息
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat(),
                    "original_timestamp": timestamp,
                    "session_id": session_id
                }))
                logger.debug(f"🏓 发送pong响应: {session_id}")

            except Exception as e:
                logger.error(f"💥 处理ping消息失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Failed to handle ping: {str(e)}"
                }))

        else:
            logger.warning(f"⚠️ 未知消息类型: {message_type}")
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }))
            
    except Exception as e:
        logger.error(f"💥 WebSocket消息处理错误: {session_id}, 错误类型={type(e).__name__}, 错误={e}")
        try:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Message processing error: {str(e)}"
            }))
        except Exception as send_error:
            logger.error(f"💥 发送错误消息失败: {send_error}")


@app.post("/api/sessions/{session_id}/interrupt")
async def manual_interrupt(session_id: str, interrupt_data: Dict[str, Any]):
    """Manual interruption trigger for testing."""
    try:
        if session_id not in conversation_sessions:
            raise HTTPException(status_code=404, detail="Session not found")
            
        # Trigger manual interruption
        await orchestrator.handle_manual_interruption(session_id, interrupt_data)
        
        return {
            "status": "success",
            "message": "Manual interruption triggered",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Manual interrupt error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/sessions")
async def list_active_sessions():
    """List all active conversation sessions."""
    return {
        "active_sessions": [
            {
                "session_id": session_id,
                "start_time": session["start_time"].isoformat(),
                "state": session["state"]
            }
            for session_id, session in conversation_sessions.items()
        ],
        "total_count": len(conversation_sessions)
    }


if __name__ == "__main__":
    # Run the FastAPI application
    uvicorn.run(
        "app:app",
        host=settings.ws_host,
        port=settings.ws_port,
        reload=True,  # Development mode
        log_level=settings.log_level.lower()
    ) 