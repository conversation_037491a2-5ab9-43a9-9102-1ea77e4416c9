"""
Configuration management for the Audio Agent system.
"""

import os
from typing import Optional, Dict, Any, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """
    System configuration using Pydantic Settings.
    Automatically loads from environment variables.
    """
    
    # 阿里千问 配置 (用于Realtime API - Listener和Speaker)
    qwen_api_key: str = Field(
        description="阿里千问 API Key"
    )
    qwen_realtime_model: str = Field(
        default="qwen-omni-turbo-realtime",
        description="阿里千问-Realtime API model name"
    )
    qwen_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/api-ws/v1/",
        description="阿里千问 API base URL"
    )
    qwen_ws_endpoint: str = Field(
        default="wss://dashscope.aliyuncs.com/api-ws/v1/realtime",
        description="阿里千问 WebSocket endpoint"
    )
    
    # 豆包 配置 (用于Tactical Thinker、Strategic Thinker和Decider)
    doubao_api_key: str = Field(
        description="字节跳动豆包 API Key"
    )
    doubao_base_url: str = Field(
        default="https://ark.cn-beijing.volces.com/api/v3",
        description="豆包 API base URL"
    )
    
    # 音频配置
    audio_sample_rate: int = Field(default=24000, description="Audio sample rate")
    audio_channels: int = Field(default=1, description="Audio channels")
    audio_format: str = Field(default="pcm16", description="Audio format")
    audio_chunk_size: int = Field(default=1024, description="Audio chunk size")
    
    # 系统配置
    log_level: str = Field(default="INFO", description="Logging level")
    max_conversations: int = Field(default=10, description="Maximum concurrent conversations")
    interruption_threshold: float = Field(default=0.5, description="General interruption threshold")
    
    # WebSocket配置
    ws_host: str = Field(default="0.0.0.0", description="WebSocket host")
    ws_port: int = Field(default=8000, description="WebSocket port")
    
    # AI Agent配置 - 豆包模型
    tactical_thinker_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Tactical thinker model")
    strategic_thinker_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Strategic thinker model")
    decider_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Decider model")
    
    # Interruption Logic Configuration (optimized for better conversation experience)
    cooperative_interruption_threshold: float = Field(
        default=0.3,
        description="Confidence threshold for Tactical Thinker's cooperative interjections - lowered for better interactivity."
    )
    disruptive_interruption_threshold: float = Field(
        default=0.4,
        description="Confidence threshold for Strategic Thinker's disruptive interruptions - balanced for smooth conversation."
    )
    silence_duration_ms: int = Field(default=800, description="Silence duration in milliseconds - optimized for responsiveness")
    response_delay_ms: int = Field(default=200, description="Response delay in milliseconds - reduced for natural conversation")

    # 🎯 优化：Tactical Thinker 缓冲区配置 - 更敏感的协作反馈
    tactical_buffer_min_length: int = Field(default=3, description="Tactical thinker minimum buffer length - lowered for quicker feedback")
    tactical_buffer_max_length: int = Field(default=15, description="Tactical thinker maximum buffer length - optimized for natural interaction")

    # 🎯 优化：Strategic Thinker 缓冲区配置 - 平衡深度分析与响应速度
    strategic_buffer_min_length: int = Field(default=5, description="Strategic thinker minimum buffer length - balanced for context analysis")
    strategic_buffer_max_length: int = Field(default=25, description="Strategic thinker maximum buffer length - allows deeper analysis")
    
    # Decider agent settings - 战略分析超时设置
    decider_strategic_timeout: int = 3  # 从5秒降低到3秒，加快决策速度

    # 🎯 VAD (语音活动检测) 配置 - 优化对话体验
    # STANDARD模式: 标准对话模式，提高灵敏度以更好地检测语音
    vad_standard_threshold: float = Field(default=0.4, description="VAD threshold for standard mode - improved sensitivity")
    vad_standard_prefix_padding_ms: int = Field(default=400, description="VAD prefix padding for standard mode - prevent speech cutoff")
    vad_standard_silence_duration_ms: int = Field(default=800, description="VAD silence duration for standard mode - faster response")

    # BARGE_IN模式: 打断模式，优化用户插话体验
    vad_barge_in_threshold: float = Field(default=0.2, description="VAD threshold for barge-in mode - easier interruption")
    vad_barge_in_prefix_padding_ms: int = Field(default=150, description="VAD prefix padding for barge-in mode - quick response")
    vad_barge_in_silence_duration_ms: int = Field(default=600, description="VAD silence duration for barge-in mode - fast switching")

    # 🎯 面试官模式配置 - 优化面试体验平衡性
    interviewer_mode: bool = False  # 面试官模式开关
    interviewer_cooperative_threshold: float = 0.4    # 适度提高，减少不必要插话
    interviewer_disruptive_threshold: float = 0.3     # 保持合理控制力
    interviewer_max_speaking_time: int = 20           # 增加用户表达时间
    interviewer_force_interrupt_after: int = 40      # 延长强制打断时间
    interviewer_topic_drift_threshold: float = 0.6   # 话题偏移检测阈值
    interviewer_verbosity_threshold: float = 0.7     # 冗长度检测阈值
    
    # 分析器超时配置 - 优化响应速度
    strategic_analysis_timeout: int = Field(default=3000, description="Strategic analysis timeout - optimized for responsiveness")
    tactical_analysis_timeout: int = Field(default=2000, description="Tactical analysis timeout - quick feedback response")

    # 🎯 新增：对话流畅性配置
    min_response_interval_ms: int = Field(default=500, description="Minimum interval between AI responses for natural flow")
    max_ai_speaking_duration_sec: int = Field(default=30, description="Maximum AI speaking duration to prevent long monologues")
    
    # 连接管理配置
    auto_reconnect: bool = Field(default=True, description="Enable automatic reconnection")

    # Paraformer实时转录配置
    enable_paraformer_transcription: bool = Field(default=True, description="Enable Paraformer real-time transcription")
    paraformer_model: str = Field(default="paraformer-realtime-v2", description="Paraformer model name")
    paraformer_language_hints: List[str] = Field(default=["zh", "en"], description="Language hints for Paraformer")
    paraformer_format: str = Field(default="pcm", description="Audio format for Paraformer")
    paraformer_sample_rate: int = Field(default=16000, description="Sample rate for Paraformer")
    paraformer_confidence_threshold: float = Field(default=0.7, description="Paraformer transcript confidence threshold - lowered for better responsiveness")

    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

# 面试官模式指令常量
INTERVIEWER_INSTRUCTIONS = """你是一个专业的AI技术面试官。你的职责是：
1. 主动引导面试流程，控制节奏
2. 在候选人偏离主题时及时打断
3. 深入挖掘技术细节，追问实现原理
4. 评估候选人的技术深度和思维逻辑
5. 保持专业但友好的面试氛围

打断策略：
- 当回答偏离技术主题时立即打断
- 当回答过于冗长或缺乏重点时适时打断  
- 当需要深入某个技术点时主动打断追问
- 控制每个问题的回答时长，保持面试效率"""

# 🎯 优化：定义标准模式下的AI身份 - 更自然的对话体验
ADVENTUREX_PERSONA = """你是Audio Manus，一个来自AdventureX活动团队的友好专业语音助手。你的主要职责是进行自然、有帮助且引人入胜的对话。

核心特质：
- 友好热情，但保持专业
- 主动参与对话，适时提供协助和反馈
- 回应简洁明了，避免冗长
- 根据用户使用的语言自动切换（中文/英文）
- 避免重复相同的回答
- 遇到不清楚的问题时礼貌地请求澄清

对话原则：
- 等待用户先开口，不要主动开始对话
- 保持对话的自然流畅性
- 适时表达理解、同意或提供建议
- 在用户需要时提供协助，但不要过度打断"""

# 将其整合到STANDARD_INSTRUCTIONS
STANDARD_INSTRUCTIONS = ADVENTUREX_PERSONA


def get_realtime_config() -> Dict[str, Any]:
    """Get 阿里千问 realtime API configuration dictionary."""
    return {
        "api_key": settings.qwen_api_key,
        "model": settings.qwen_realtime_model,
        "base_url": settings.qwen_base_url,
        "ws_endpoint": settings.qwen_ws_endpoint
    }


def get_qwen_voice_mapping() -> Dict[str, str]:
    """Get a clear, unambiguous voice name mapping for qwen API."""
    return {
        # Female Voices
        "alloy": "Serena", 
        "echo": "Serena", # Use the same target for similar voices
        "nova": "Cherry", # Assign a different voice for variety

        # Male Voices
        "onyx": "Ethan",
        "shimmer": "Ethan",
        "fable": "Mike", # Assign a different male voice
    }


def get_doubao_config() -> Dict[str, Any]:
    """Get 字节跳动豆包 API configuration dictionary."""
    return {
        "api_key": settings.doubao_api_key,
        "base_url": settings.doubao_base_url
    }


def get_audio_config() -> Dict[str, Any]:
    """Get audio configuration dictionary."""
    return {
        "sample_rate": settings.audio_sample_rate,
        "channels": settings.audio_channels,
        "format": settings.audio_format,
        "chunk_size": settings.audio_chunk_size
    } 